# Romanized Tamil Implementation Guide

## 🎯 Quick Reference for Implementing Romanized Tamil in NIRA

This guide provides the exact steps needed to implement romanized Tamil pronunciation display in any NIRA lesson, based on the successful fix applied to the Directions and Locations lesson.

---

## 🔧 Required Database Schema Changes

### 1. Conversations Table
```sql
-- Add pronunciation field to conversations
ALTER TABLE conversations ADD COLUMN pronunciation TEXT;

-- Example data structure:
{
  "speaker": "Person A",
  "text": "மன்னிக்கவும், கோயில் எங்கே இருக்கிறது?",
  "translation": "Excuse me, where is the temple?",
  "pronunciation": "mannikkavum, koyil engE irukkiRadhu?",
  "audio_url": "https://..."
}
```

### 2. Exercises Table
```sql
-- Add options_pronunciations array to exercises
ALTER TABLE exercises ADD COLUMN options_pronunciations TEXT[];

-- Example data structure:
{
  "question": "What is the Tamil word for 'north'?",
  "options": ["வடக்கு", "தெற்கு", "கிழக்கு", "மேற்கு"],
  "options_pronunciations": ["vadakku", "therku", "kizhakku", "merku"],
  "correctAnswer": 0,
  "explanation": "வடக்கு (vadakku) means north in Tamil"
}
```

---

## 📱 Required iOS Model Updates

### 1. LessonDialogueItem Model
```swift
struct LessonDialogueItem {
    let speaker: String
    let text: String
    let translation: String?
    let pronunciation: String? // ✅ ADD THIS FIELD
    let culturalNote: String?
    let audioURL: String?
}
```

### 2. LessonExerciseItem Model
```swift
struct LessonExerciseItem {
    let type: String
    let question: String
    let options: [String]
    let correctAnswer: Int
    let explanation: String?
    let points: Int
    let optionsPronunciations: [String]? // ✅ ADD THIS FIELD
}
```

---

## 🔄 Required Data Extraction Updates

### 1. Conversation Extraction (LessonDetailView.swift)
```swift
// BEFORE (missing pronunciation):
let dialogueItem = LessonDialogueItem(
    speaker: speaker,
    text: text,
    translation: exchange["translation"] as? String,
    culturalNote: nil,
    audioURL: exchange["audio_url"] as? String
)

// AFTER (includes pronunciation):
let dialogueItem = LessonDialogueItem(
    speaker: speaker,
    text: text,
    translation: exchange["translation"] as? String,
    pronunciation: exchange["pronunciation"] as? String, // ✅ ADD THIS
    culturalNote: nil,
    audioURL: exchange["audio_url"] as? String
)
```

### 2. Exercise Extraction (LessonDetailView.swift)
```swift
// BEFORE (missing pronunciations):
return LessonExerciseItem(
    type: type,
    question: question,
    options: exerciseDict["options"] as? [String] ?? [],
    correctAnswer: exerciseDict["correct_answer"] as? Int ?? 0,
    explanation: exerciseDict["explanation"] as? String,
    points: exerciseDict["points"] as? Int ?? 10
)

// AFTER (includes pronunciations):
return LessonExerciseItem(
    type: type,
    question: question,
    options: exerciseDict["options"] as? [String] ?? [],
    correctAnswer: exerciseDict["correct_answer"] as? Int ?? 0,
    explanation: exerciseDict["explanation"] as? String,
    points: exerciseDict["points"] as? Int ?? 10,
    optionsPronunciations: exerciseDict["options_pronunciations"] as? [String] // ✅ ADD THIS
)
```

---

## 🎨 Required UI Implementation Updates

### 1. ConversationDetailView.swift
```swift
// BEFORE (using incomplete romanize function):
Text("[\(romanize(conversation.text))]")
    .font(.system(size: 16, weight: .medium))
    .foregroundColor(.green)
    .italic()

// AFTER (using database pronunciation):
if let pronunciation = conversation.pronunciation {
    Text("[\(pronunciation)]")
        .font(.system(size: 16, weight: .medium))
        .foregroundColor(.green)
        .italic()
        .multilineTextAlignment(.center)
        .padding(.horizontal, 20)
} else {
    // Fallback only if pronunciation missing
    Text("[\(romanize(conversation.text))]")
        .font(.system(size: 16, weight: .medium))
        .foregroundColor(.green)
        .italic()
        .multilineTextAlignment(.center)
        .padding(.horizontal, 20)
}
```

### 2. ExerciseDetailView.swift
```swift
// BEFORE (no pronunciation for options):
VStack(alignment: .leading, spacing: 4) {
    Text(option)
        .font(.system(size: 16))
        .foregroundColor(.primary)
        .multilineTextAlignment(.leading)
}

// AFTER (includes pronunciation for each option):
VStack(alignment: .leading, spacing: 4) {
    Text(option)
        .font(.system(size: 16))
        .foregroundColor(.primary)
        .multilineTextAlignment(.leading)

    // Show romanization if available for this option
    if let pronunciations = exercise.optionsPronunciations,
       index < pronunciations.count {
        Text("[\(pronunciations[index])]")
            .font(.system(size: 14))
            .foregroundColor(.green)
            .italic()
    }
}
```

### 3. LessonDetailView.swift (Exercise Cards)
```swift
// BEFORE (no pronunciation in lesson overview):
Text(option)
    .font(.system(size: 14))
    .foregroundColor(.primary)

// AFTER (includes pronunciation in overview):
VStack(alignment: .leading, spacing: 2) {
    Text(option)
        .font(.system(size: 14))
        .foregroundColor(.primary)

    // Show romanization if available for this option
    if let pronunciations = exercise.optionsPronunciations,
       index < pronunciations.count {
        Text("[\(pronunciations[index])]")
            .font(.system(size: 12))
            .foregroundColor(.green)
            .italic()
    }
}
```

---

## 🗂️ Required Preview Data Updates

### 1. ConversationDetailView Preview
```swift
// BEFORE (missing pronunciation):
LessonDialogueItem(
    speaker: "Person A",
    text: "வணக்கம்!",
    translation: "Hello!",
    culturalNote: "This is the most common greeting in Tamil.",
    audioURL: nil
)

// AFTER (includes pronunciation):
LessonDialogueItem(
    speaker: "Person A",
    text: "வணக்கம்!",
    translation: "Hello!",
    pronunciation: "vanakkam!", // ✅ ADD THIS
    culturalNote: "This is the most common greeting in Tamil.",
    audioURL: nil
)
```

### 2. ExerciseDetailView Preview
```swift
// BEFORE (missing pronunciations):
LessonExerciseItem(
    type: "multiple_choice",
    question: "What is the Tamil word for 'dog'?",
    options: ["நாய்", "பூனை", "யானை", "சிங்கம்"],
    correctAnswer: 0,
    explanation: "நாய் means dog in Tamil",
    points: 10
)

// AFTER (includes pronunciations):
LessonExerciseItem(
    type: "multiple_choice",
    question: "What is the Tamil word for 'dog'?",
    options: ["நாய்", "பூனை", "யானை", "சிங்கம்"],
    correctAnswer: 0,
    explanation: "நாய் means dog in Tamil",
    points: 10,
    optionsPronunciations: ["naay", "poonai", "yaanai", "singam"] // ✅ ADD THIS
)
```

---

## 🧪 Testing and Validation

### 1. Database Validation Script
```python
def validate_pronunciations(lesson_id):
    """Validate all pronunciation data exists"""
    
    # Check conversations
    conversations = get_conversations(lesson_id)
    for conv in conversations:
        assert conv.get('pronunciation'), f"Missing pronunciation: {conv['text']}"
    
    # Check exercises
    exercises = get_exercises(lesson_id)
    for exercise in exercises:
        options = exercise.get('options', [])
        pronunciations = exercise.get('options_pronunciations', [])
        assert len(options) == len(pronunciations), "Pronunciation count mismatch"
```

### 2. iOS Testing Checklist
```
□ Conversations show green romanized text below Tamil
□ Exercise options show green romanized text in brackets
□ No "romanize() function" errors
□ App builds without compilation errors
□ All pronunciations are non-empty
□ Pronunciation arrays match option arrays
```

---

## 🚨 Common Pitfalls to Avoid

### 1. **DON'T** rely on romanize() function
- Limited dictionary coverage
- Cannot handle complex phrases
- Always use database pronunciation data

### 2. **DON'T** forget preview data updates
- Missing fields cause compilation errors
- Update all sample data with pronunciations

### 3. **DON'T** skip array length validation
- Options and pronunciations must match in count
- Empty pronunciations cause UI issues

### 4. **DON'T** ignore UI consistency
- Always use green color for romanized text
- Maintain consistent font sizes and styling

---

## ✅ Success Criteria

**Implementation is complete when:**
- [ ] All conversations show romanized Tamil in green
- [ ] All exercise options show pronunciations in brackets
- [ ] App builds without errors
- [ ] No missing pronunciation data
- [ ] UI is consistent and readable
- [ ] Database properly populated

**This implementation ensures proper romanized Tamil display throughout the NIRA app, improving the learning experience for Tamil language students.**
