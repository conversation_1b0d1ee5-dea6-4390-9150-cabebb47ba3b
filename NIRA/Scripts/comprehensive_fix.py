#!/usr/bin/env python3
"""
Comprehensive Fix for NIRA iOS App Issues
Fixes all potential sequence_order issues across all Tamil paths

This script:
1. Finds ALL Tamil learning paths
2. Fixes sequence_order for ALL lessons in ALL paths
3. Ensures no null values exist anywhere
4. Provides detailed debugging info for iOS app
"""

import requests
import time
from typing import Dict, List, Any

# Supabase configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# Tamil language ID
TAMIL_LANGUAGE_ID = "4df28de4-168f-4d8e-a304-b785e9295644"

class ComprehensiveFixer:
    """Comprehensive fixer for all Tamil lesson issues"""
    
    def __init__(self):
        self.headers = {
            'apikey': SUPABASE_KEY,
            'Authorization': f'Bearer {SUPABASE_KEY}',
            'Content-Type': 'application/json'
        }
    
    def get_all_tamil_paths(self) -> List[Dict[str, Any]]:
        """Get all Tamil learning paths"""
        print("🔍 Finding all Tamil learning paths...")
        
        params = {
            'select': 'id,name,level',
            'language_id': f'eq.{TAMIL_LANGUAGE_ID}'
        }
        
        response = requests.get(f"{SUPABASE_URL}/rest/v1/learning_paths", headers=self.headers, params=params)
        
        if response.status_code == 200:
            paths = response.json()
            print(f"✅ Found {len(paths)} Tamil learning paths")
            for path in paths:
                print(f"  • {path.get('name', 'Unknown')} ({path.get('level', 'Unknown')}) - {path['id']}")
            return paths
        else:
            print(f"❌ Error fetching paths: {response.status_code}")
            return []
    
    def fix_path_lessons(self, path_id: str, path_name: str) -> Dict[str, Any]:
        """Fix all lessons in a specific path"""
        print(f"\n🔧 Fixing lessons in: {path_name}")
        
        # Get all lessons in this path
        params = {
            'select': 'id,title,sequence_order,difficulty_level',
            'path_id': f'eq.{path_id}',
            'order': 'created_at'
        }
        
        response = requests.get(f"{SUPABASE_URL}/rest/v1/lessons", headers=self.headers, params=params)
        
        if response.status_code != 200:
            print(f"❌ Error fetching lessons: {response.status_code}")
            return {'fixed': 0, 'errors': 1}
        
        lessons = response.json()
        print(f"  📚 Found {len(lessons)} lessons")
        
        if not lessons:
            return {'fixed': 0, 'errors': 0}
        
        # Sort lessons by difficulty level, then by title for consistency
        lessons.sort(key=lambda x: (x.get('difficulty_level', 1), x.get('title', '')))
        
        fixed_count = 0
        error_count = 0
        
        for i, lesson in enumerate(lessons, 1):
            lesson_id = lesson['id']
            title = lesson.get('title', 'Unknown')
            current_sequence = lesson.get('sequence_order')
            new_sequence = i
            
            # Always update to ensure no null values
            try:
                update_data = {'sequence_order': new_sequence}
                
                response = requests.patch(
                    f"{SUPABASE_URL}/rest/v1/lessons?id=eq.{lesson_id}",
                    json=update_data,
                    headers=self.headers
                )
                
                if response.status_code in [200, 204]:
                    if current_sequence != new_sequence:
                        print(f"  ✅ Fixed: {title} ({current_sequence} → {new_sequence})")
                        fixed_count += 1
                    else:
                        print(f"  ✓ OK: {title} (sequence: {new_sequence})")
                else:
                    print(f"  ❌ Failed: {title} - {response.status_code}")
                    error_count += 1
                
                time.sleep(0.3)
                
            except Exception as e:
                print(f"  ❌ Error: {title} - {e}")
                error_count += 1
        
        return {'fixed': fixed_count, 'errors': error_count}
    
    def validate_all_paths(self) -> Dict[str, Any]:
        """Validate all Tamil paths"""
        print("\n🔍 Validating all Tamil paths...")
        
        paths = self.get_all_tamil_paths()
        validation_results = {}
        
        for path in paths:
            path_id = path['id']
            path_name = path.get('name', 'Unknown')
            
            # Get lessons for validation
            params = {
                'select': 'id,title,sequence_order',
                'path_id': f'eq.{path_id}',
                'order': 'sequence_order'
            }
            
            response = requests.get(f"{SUPABASE_URL}/rest/v1/lessons", headers=self.headers, params=params)
            
            if response.status_code == 200:
                lessons = response.json()
                
                # Check for issues
                issues = []
                null_count = 0
                gap_count = 0
                
                for i, lesson in enumerate(lessons):
                    seq = lesson.get('sequence_order')
                    if seq is None:
                        null_count += 1
                        issues.append(f"Index {i}: NULL sequence_order")
                    elif seq != i + 1:
                        gap_count += 1
                        issues.append(f"Index {i}: Expected {i+1}, got {seq}")
                
                validation_results[path_name] = {
                    'lesson_count': len(lessons),
                    'null_count': null_count,
                    'gap_count': gap_count,
                    'issues': issues,
                    'status': 'PASS' if not issues else 'FAIL'
                }
                
                print(f"  • {path_name}: {len(lessons)} lessons - {validation_results[path_name]['status']}")
                if issues:
                    for issue in issues[:3]:  # Show first 3 issues
                        print(f"    - {issue}")
                    if len(issues) > 3:
                        print(f"    - ... and {len(issues) - 3} more issues")
            else:
                validation_results[path_name] = {
                    'status': 'ERROR',
                    'error': f"HTTP {response.status_code}"
                }
        
        return validation_results
    
    def create_ios_debug_info(self) -> None:
        """Create debug info for iOS app"""
        print("\n📱 Creating iOS Debug Info...")
        
        # Get the main Tamil A1 path that iOS app likely uses
        main_path_id = "6b427613-420f-4586-bce8-2773d722f0b4"
        
        params = {
            'select': 'id,title,sequence_order,difficulty_level,created_at',
            'path_id': f'eq.{main_path_id}',
            'order': 'sequence_order'
        }
        
        response = requests.get(f"{SUPABASE_URL}/rest/v1/lessons", headers=self.headers, params=params)
        
        if response.status_code == 200:
            lessons = response.json()
            
            print(f"📊 Main Tamil A1 Path Debug Info:")
            print(f"  • Path ID: {main_path_id}")
            print(f"  • Total Lessons: {len(lessons)}")
            print(f"  • First 5 lessons:")
            
            for i, lesson in enumerate(lessons[:5]):
                seq = lesson.get('sequence_order')
                title = lesson.get('title', 'No title')
                print(f"    Index {i}: seq={seq} - {title}")
                
                if seq is None:
                    print(f"      ⚠️ NULL SEQUENCE ORDER AT INDEX {i}")
                elif seq != i + 1:
                    print(f"      ⚠️ SEQUENCE MISMATCH AT INDEX {i} (expected {i+1}, got {seq})")
            
            # Check specifically index 2 (where iOS error occurs)
            if len(lessons) > 2:
                lesson_2 = lessons[2]
                seq_2 = lesson_2.get('sequence_order')
                title_2 = lesson_2.get('title', 'No title')
                print(f"\n🎯 Index 2 (iOS Error Location):")
                print(f"  • Title: {title_2}")
                print(f"  • Sequence Order: {seq_2}")
                print(f"  • Type: {type(seq_2)}")
                print(f"  • Is None: {seq_2 is None}")
                
                if seq_2 is None:
                    print(f"  ❌ FOUND THE ISSUE: Index 2 has NULL sequence_order")
                else:
                    print(f"  ✅ Index 2 looks good")
        
        print(f"\n📋 iOS App Troubleshooting:")
        print(f"  1. Clear app cache/data")
        print(f"  2. Force close and restart app")
        print(f"  3. Check if app is using correct path ID: {main_path_id}")
        print(f"  4. Verify app is calling: /rest/v1/lessons?path_id=eq.{main_path_id}")
    
    def fix_everything(self) -> Dict[str, Any]:
        """Fix all issues across all Tamil paths"""
        print("🚀 COMPREHENSIVE TAMIL LESSON FIXER")
        print("Fixing ALL sequence_order issues for iOS app")
        print("=" * 60)
        
        # Get all Tamil paths
        paths = self.get_all_tamil_paths()
        
        if not paths:
            return {'status': 'failed', 'error': 'No Tamil paths found'}
        
        # Fix each path
        total_fixed = 0
        total_errors = 0
        
        for path in paths:
            path_id = path['id']
            path_name = path.get('name', 'Unknown')
            
            result = self.fix_path_lessons(path_id, path_name)
            total_fixed += result['fixed']
            total_errors += result['errors']
        
        # Validate everything
        validation_results = self.validate_all_paths()
        
        # Create iOS debug info
        self.create_ios_debug_info()
        
        return {
            'total_paths': len(paths),
            'total_fixed': total_fixed,
            'total_errors': total_errors,
            'validation_results': validation_results
        }

def main():
    """Main function"""
    print("🎯 COMPREHENSIVE TAMIL LESSON FIXER")
    print("Fixing iOS app sequence_order issues")
    print("=" * 50)
    
    fixer = ComprehensiveFixer()
    
    # Fix everything
    results = fixer.fix_everything()
    
    # Display results
    print(f"\n📊 COMPREHENSIVE FIX RESULTS:")
    print(f"  • Total Paths: {results.get('total_paths', 0)}")
    print(f"  • Total Fixed: {results.get('total_fixed', 0)}")
    print(f"  • Total Errors: {results.get('total_errors', 0)}")
    
    validation_results = results.get('validation_results', {})
    passed = sum(1 for r in validation_results.values() if r.get('status') == 'PASS')
    failed = sum(1 for r in validation_results.values() if r.get('status') == 'FAIL')
    
    print(f"  • Validation: {passed} passed, {failed} failed")
    
    if failed == 0:
        print(f"\n🎉 SUCCESS! All Tamil paths fixed")
        print(f"📱 iOS app should work now - try restarting the app")
    else:
        print(f"\n⚠️ Some issues remain - check validation details above")

if __name__ == "__main__":
    main()
