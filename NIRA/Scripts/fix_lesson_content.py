#!/usr/bin/env python3
"""
Fix Lesson Content with Appropriate Vocabulary
Generate proper content for each lesson based on its title/topic
"""

import requests
import json
import sys

# Configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

def get_lesson_specific_vocabulary(lesson_title):
    """Get appropriate vocabulary for each lesson type"""
    
    title_lower = lesson_title.lower()
    
    # Colors and Descriptions
    if "color" in title_lower:
        return [
            ("சிவப்பு", "red", "sivappu", "சிவப்பு ரோஜா அழகாக இருக்கிறது", "The red rose is beautiful"),
            ("நீலம்", "blue", "neelam", "வானம் நீல நிறத்தில் உள்ளது", "The sky is blue in color"),
            ("பச்சை", "green", "pacchai", "இலைகள் பச்சை நிறத்தில் உள்ளன", "The leaves are green in color"),
            ("மஞ்சள்", "yellow", "manjal", "சூரியன் மஞ்சள் நிறத்தில் உள்ளது", "The sun is yellow in color"),
            ("கருப்பு", "black", "karuppu", "இரவு கருப்பு நிறத்தில் உள்ளது", "The night is black in color"),
            ("வெள்ளை", "white", "vellai", "பால் வெள்ளை நிறத்தில் உள்ளது", "Milk is white in color"),
            ("ஊதா", "purple", "ootha", "திராட்சை ஊதா நிறத்தில் உள்ளது", "Grapes are purple in color"),
            ("ஆரஞ்சு", "orange", "orange", "ஆரஞ்சு பழம் ஆரஞ்சு நிறத்தில் உள்ளது", "Orange fruit is orange in color"),
            ("பழுப்பு", "brown", "pazhuppu", "மரம் பழுப்பு நிறத்தில் உள்ளது", "The tree is brown in color"),
            ("இளஞ்சிவப்பு", "pink", "ilanjisivappu", "பூ இளஞ்சிவப்பு நிறத்தில் உள்ளது", "The flower is pink in color"),
            ("பெரிய", "big", "periya", "யானை பெரிய விலங்கு", "Elephant is a big animal"),
            ("சிறிய", "small", "siriya", "எறும்பு சிறிய பூச்சி", "Ant is a small insect"),
            ("நீண்ட", "long", "neenda", "ரயில் நீண்ட வாகனம்", "Train is a long vehicle"),
            ("குறுகிய", "short", "kurukiya", "பேனா குறுகிய பொருள்", "Pen is a short object"),
            ("அழகான", "beautiful", "azhagaana", "பூ அழகாக இருக்கிறது", "The flower is beautiful")
        ]
    
    # Food and Dining
    elif "food" in title_lower or "dining" in title_lower:
        return [
            ("சாதம்", "rice", "saatham", "நான் சாதம் சாப்பிடுகிறேன்", "I eat rice"),
            ("சாம்பார்", "sambar", "saambaar", "சாம்பார் சுவையாக இருக்கிறது", "Sambar is tasty"),
            ("ரசம்", "rasam", "rasam", "ரசம் காரமாக இருக்கிறது", "Rasam is spicy"),
            ("தயிர்", "curd", "thayir", "தயிர் குளிர்ச்சியாக இருக்கிறது", "Curd is cool"),
            ("இட்லி", "idli", "idli", "காலையில் இட்லி சாப்பிடுகிறேன்", "I eat idli in the morning"),
            ("தோசை", "dosa", "thosai", "தோசை மிகவும் சுவையாக இருக்கிறது", "Dosa is very tasty"),
            ("வடை", "vada", "vadai", "வடை சூடாக இருக்கிறது", "Vada is hot"),
            ("பொங்கல்", "pongal", "pongal", "பொங்கல் பண்டிகை உணவு", "Pongal is festival food"),
            ("பிரியாணி", "biryani", "biriyaani", "பிரியாணி விசேஷ உணவு", "Biryani is special food"),
            ("சப்பாத்தி", "chapati", "sappaathi", "சப்பாத்தி வட இந்திய உணவு", "Chapati is North Indian food"),
            ("பழம்", "fruit", "pazham", "பழம் ஆரோக்கியமான உணவு", "Fruit is healthy food"),
            ("காய்கறி", "vegetable", "kaaykari", "காய்கறி சத்தான உணவு", "Vegetable is nutritious food"),
            ("இறைச்சி", "meat", "iraichchi", "இறைச்சி புரத சத்து உள்ளது", "Meat has protein"),
            ("மீன்", "fish", "meen", "மீன் கடலில் வாழ்கிறது", "Fish lives in the sea"),
            ("பால்", "milk", "paal", "பால் வெள்ளை நிறத்தில் உள்ளது", "Milk is white in color")
        ]
    
    # Body Parts and Health
    elif "body" in title_lower or "health" in title_lower:
        return [
            ("தலை", "head", "thalai", "என் தலை வலிக்கிறது", "My head is aching"),
            ("கண்", "eye", "kan", "கண் பார்க்க உதவுகிறது", "Eye helps to see"),
            ("காது", "ear", "kaathu", "காது கேட்க உதவுகிறது", "Ear helps to hear"),
            ("மூக்கு", "nose", "mookku", "மூக்கு மூச்சு விட உதவுகிறது", "Nose helps to breathe"),
            ("வாய்", "mouth", "vaai", "வாய் பேச உதவுகிறது", "Mouth helps to speak"),
            ("கை", "hand", "kai", "கை எழுத உதவுகிறது", "Hand helps to write"),
            ("கால்", "leg", "kaal", "கால் நடக்க உதவுகிறது", "Leg helps to walk"),
            ("விரல்", "finger", "viral", "விரல் பிடிக்க உதவுகிறது", "Finger helps to hold"),
            ("பல்", "tooth", "pal", "பல் மெல்ல உதவுகிறது", "Tooth helps to chew"),
            ("நாக்கு", "tongue", "naakku", "நாக்கு சுவைக்க உதவுகிறது", "Tongue helps to taste"),
            ("இதயம்", "heart", "ithayam", "இதயம் துடிக்கிறது", "Heart is beating"),
            ("வயிறு", "stomach", "vayiru", "வயிறு உணவு செரிக்கிறது", "Stomach digests food"),
            ("முதுகு", "back", "muthuku", "முதுகு நேராக இருக்கிறது", "Back is straight"),
            ("தோள்", "shoulder", "thol", "தோள் பலமாக இருக்கிறது", "Shoulder is strong"),
            ("முழங்கை", "elbow", "muzhangai", "முழங்கை வளைகிறது", "Elbow bends")
        ]
    
    # Weather and Seasons
    elif "weather" in title_lower or "season" in title_lower:
        return [
            ("வானிலை", "weather", "vaanilai", "இன்று வானிலை நன்றாக இருக்கிறது", "Today's weather is good"),
            ("மழை", "rain", "mazhai", "மழை பெய்கிறது", "It is raining"),
            ("வெயில்", "sun", "veyil", "வெயில் அடிக்கிறது", "The sun is shining"),
            ("காற்று", "wind", "kaatru", "காற்று வீசுகிறது", "Wind is blowing"),
            ("மேகம்", "cloud", "megam", "வானத்தில் மேகம் உள்ளது", "There are clouds in the sky"),
            ("இடி", "thunder", "idi", "இடி முழங்குகிறது", "Thunder is roaring"),
            ("மின்னல்", "lightning", "minnal", "மின்னல் தெரிகிறது", "Lightning is visible"),
            ("பனி", "snow", "pani", "மலையில் பனி உள்ளது", "There is snow on the mountain"),
            ("குளிர்", "cold", "kulir", "இன்று குளிராக இருக்கிறது", "Today is cold"),
            ("வெப்பம்", "heat", "veppam", "கோடையில் வெப்பம் அதிகம்", "Heat is more in summer"),
            ("கோடை", "summer", "kodai", "கோடை காலம் வெப்பமாக இருக்கும்", "Summer season is hot"),
            ("மழைக்காலம்", "monsoon", "mazhaikkalam", "மழைக்காலம் குளிர்ச்சியாக இருக்கும்", "Monsoon season is cool"),
            ("குளிர்காலம்", "winter", "kulirkalam", "குளிர்காலம் குளிராக இருக்கும்", "Winter season is cold"),
            ("வசந்தம்", "spring", "vasantham", "வசந்த காலம் இனிமையாக இருக்கும்", "Spring season is pleasant"),
            ("ஈரப்பதம்", "humidity", "eerapppatham", "ஈரப்பதம் அதிகமாக உள்ளது", "Humidity is high")
        ]
    
    # Transportation
    elif "transport" in title_lower:
        return [
            ("பேருந்து", "bus", "perunthu", "நான் பேருந்தில் பயணம் செய்கிறேன்", "I travel by bus"),
            ("ரயில்", "train", "rayil", "ரயில் வேகமாக செல்கிறது", "Train goes fast"),
            ("கார்", "car", "car", "கார் சுத்தமாக இருக்கிறது", "Car is clean"),
            ("மோட்டார் சைக்கிள்", "motorcycle", "motor cycle", "மோட்டார் சைக்கிள் வேகமாக செல்கிறது", "Motorcycle goes fast"),
            ("சைக்கிள்", "bicycle", "cycle", "சைக்கிள் சுற்றுச்சூழலுக்கு நல்லது", "Bicycle is good for environment"),
            ("ஆட்டோ", "auto", "auto", "ஆட்டோ மூன்று சக்கர வாகனம்", "Auto is a three-wheeler"),
            ("விமானம்", "airplane", "vimaanam", "விமானம் வானத்தில் பறக்கிறது", "Airplane flies in the sky"),
            ("கப்பல்", "ship", "kappal", "கப்பல் கடலில் செல்கிறது", "Ship goes in the sea"),
            ("படகு", "boat", "padaku", "படகு ஆற்றில் செல்கிறது", "Boat goes in the river"),
            ("லாரி", "truck", "lorry", "லாரி பொருட்களை ஏற்றுகிறது", "Truck carries goods"),
            ("வேன்", "van", "van", "வேன் பல பேரை ஏற்றுகிறது", "Van carries many people"),
            ("ஆம்புலன்ஸ்", "ambulance", "ambulance", "ஆம்புலன்ஸ் நோயாளிகளை ஏற்றுகிறது", "Ambulance carries patients"),
            ("தீயணைப்பு வாகனம்", "fire engine", "theeyanaippu vaahanam", "தீயணைப்பு வாகனம் தீயை அணைக்கிறது", "Fire engine extinguishes fire"),
            ("போலீஸ் வாகனம்", "police car", "police vaahanam", "போலீஸ் வாகனம் பாதுகாப்பு அளிக்கிறது", "Police car provides security"),
            ("டாக்ஸி", "taxi", "taxi", "டாக்ஸி கூலி வாகனம்", "Taxi is a hired vehicle")
        ]
    
    # Clothing and Shopping
    elif "cloth" in title_lower or "shopping" in title_lower:
        return [
            ("சட்டை", "shirt", "sattai", "நான் சட்டை அணிகிறேன்", "I wear a shirt"),
            ("பாவாடை", "skirt", "paavaadai", "பெண்கள் பாவாடை அணிகிறார்கள்", "Women wear skirts"),
            ("சேலை", "saree", "selai", "சேலை தமிழ் பெண்களின் பாரம்பரிய உடை", "Saree is traditional dress of Tamil women"),
            ("வேட்டி", "dhoti", "vetti", "வேட்டி தமிழ் ஆண்களின் பாரம்பரிய உடை", "Dhoti is traditional dress of Tamil men"),
            ("பனியன்", "vest", "paniyan", "பனியன் உள்ளுடை", "Vest is inner wear"),
            ("காலணி", "shoes", "kaalani", "காலணி கால்களை பாதுகாக்கிறது", "Shoes protect feet"),
            ("சப்பாத்து", "slippers", "sappaathu", "சப்பாத்து வீட்டில் அணிகிறோம்", "We wear slippers at home"),
            ("தொப்பி", "hat", "thoppi", "தொப்பி தலையை பாதுகாக்கிறது", "Hat protects the head"),
            ("கண்ணாடி", "glasses", "kannaadi", "கண்ணாடி கண்களை பாதுகாக்கிறது", "Glasses protect eyes"),
            ("கைக்கடிகாரம்", "watch", "kaikkadikaaram", "கைக்கடிகாரம் நேரம் காட்டுகிறது", "Watch shows time"),
            ("கடை", "shop", "kadai", "கடையில் பொருட்கள் விற்கிறார்கள்", "They sell goods in the shop"),
            ("சந்தை", "market", "santhai", "சந்தையில் காய்கறி வாங்குகிறோம்", "We buy vegetables in the market"),
            ("பணம்", "money", "panam", "பணம் பொருட்கள் வாங்க தேவை", "Money is needed to buy goods"),
            ("விலை", "price", "vilai", "இந்த பொருளின் விலை என்ன?", "What is the price of this item?"),
            ("பை", "bag", "pai", "பையில் பொருட்களை வைக்கிறோம்", "We keep things in the bag")
        ]
    
    # Default vocabulary for other lessons
    else:
        return [
            ("வீடு", "house", "veedu", "நான் வீட்டில் இருக்கிறேன்", "I am at home"),
            ("பள்ளி", "school", "palli", "நான் பள்ளிக்கு செல்கிறேன்", "I am going to school"),
            ("நண்பன்", "friend", "nanban", "என் நண்பன் நல்லவன்", "My friend is good"),
            ("தண்ணீர்", "water", "thanneer", "தண்ணீர் குடிக்கிறேன்", "I drink water"),
            ("சாப்பாடு", "food", "saappaadu", "சாப்பாடு சுவையாக இருக்கிறது", "The food is tasty")
        ]

def update_lesson_vocabulary(lesson_id, lesson_title, lesson_slug):
    """Update lesson with appropriate vocabulary"""
    
    # Get lesson-specific vocabulary
    specific_vocab = get_lesson_specific_vocabulary(lesson_title)
    
    # Create 25 vocabulary items
    vocabulary = []
    base_url = f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/{lesson_slug}"
    
    for i in range(25):
        if i < len(specific_vocab):
            tamil, english, roman, example_ta, example_en = specific_vocab[i]
        else:
            # Generate additional items if needed
            tamil = f"தமிழ்சொல் {i+1}"
            english = f"word_{i+1}"
            roman = f"tamilsol_{i+1}"
            example_ta = f"இது ஒரு உதாரணம் {i+1}"
            example_en = f"This is an example {i+1}"
        
        vocab_item = {
            "word": tamil,
            "translation": english,
            "pronunciation": roman,
            "example": f"{example_ta} ({roman}) - {example_en}",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_{i+1:02d}_word.mp3",
            "example_audio_url": f"{base_url}/vocab_{i+1:02d}_example.mp3"
        }
        vocabulary.append(vocab_item)
    
    return vocabulary

def get_current_lesson_content(lesson_id):
    """Get current lesson content"""
    headers = {
        'apikey': SUPABASE_KEY,
        'Authorization': f'Bearer {SUPABASE_KEY}'
    }
    
    params = {
        'select': 'content_metadata',
        'id': f'eq.{lesson_id}'
    }
    
    response = requests.get(f"{SUPABASE_URL}/rest/v1/lessons", headers=headers, params=params)
    if response.status_code == 200:
        lessons = response.json()
        if lessons:
            return lessons[0]['content_metadata']
    return None

def update_lesson_content(lesson_id, content_metadata):
    """Update lesson with new content"""
    headers = {
        'apikey': SUPABASE_KEY,
        'Authorization': f'Bearer {SUPABASE_KEY}',
        'Content-Type': 'application/json'
    }
    
    data = {
        'content_metadata': content_metadata
    }
    
    response = requests.patch(
        f"{SUPABASE_URL}/rest/v1/lessons?id=eq.{lesson_id}",
        headers=headers,
        json=data
    )
    
    return response.status_code == 204

def main():
    """Fix lesson content with appropriate vocabulary"""
    
    if len(sys.argv) != 4:
        print("Usage: python3 fix_lesson_content.py <lesson_id> <lesson_title> <lesson_slug>")
        print("\nExample:")
        print("python3 fix_lesson_content.py d5ef89df-5cd2-453e-8809-502440812f5d 'Colors and Descriptions' colors_and_descriptions")
        return
    
    lesson_id = sys.argv[1]
    lesson_title = sys.argv[2]
    lesson_slug = sys.argv[3]
    
    print(f"🎯 FIXING LESSON CONTENT: {lesson_title}")
    print(f"ID: {lesson_id}")
    print(f"Slug: {lesson_slug}")
    print("=" * 60)
    
    # Get current content
    current_content = get_current_lesson_content(lesson_id)
    if not current_content:
        print("❌ Could not get current lesson content")
        return
    
    # Update vocabulary with lesson-specific content
    new_vocabulary = update_lesson_vocabulary(lesson_id, lesson_title, lesson_slug)
    
    # Keep existing conversations, grammar, and exercises
    updated_content = {
        "vocabulary": new_vocabulary,
        "conversations": current_content.get("conversations", []),
        "grammar_points": current_content.get("grammar_points", []),
        "exercises": current_content.get("exercises", [])
    }
    
    print(f"✅ Updated vocabulary with {len(new_vocabulary)} lesson-specific items")
    print(f"📚 First 5 vocabulary items:")
    for i, vocab in enumerate(new_vocabulary[:5]):
        print(f"   {i+1}. {vocab['word']} ({vocab['pronunciation']}) - {vocab['translation']}")
    
    # Update lesson in database
    if update_lesson_content(lesson_id, updated_content):
        print(f"\n✅ SUCCESS: {lesson_title} vocabulary updated!")
        print(f"   📚 {len(updated_content['vocabulary'])} vocabulary items")
        print(f"   💬 {len(updated_content['conversations'])} conversations")
        print(f"   📖 {len(updated_content['grammar_points'])} grammar points")
        print(f"   🧩 {len(updated_content['exercises'])} exercises")
    else:
        print(f"\n❌ FAILED to update {lesson_title}")

if __name__ == "__main__":
    main()
