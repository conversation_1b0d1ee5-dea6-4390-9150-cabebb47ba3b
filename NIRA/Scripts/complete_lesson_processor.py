#!/usr/bin/env python3
"""
Complete Lesson Processor for NIRA
Ensures ALL content sections are properly generated following the Animals & Nature pattern

This processor specifically addresses the issue where lessons have vocabulary but missing:
- Conversations (15 required)
- Grammar Points (10 required) 
- Exercises (5+ required)
"""

import os
import sys
import json
import requests
import time
from typing import Dict, List, Any
import google.generativeai as genai

# API Configuration
GEMINI_API_KEY = "AIzaSyAYasSocME9nPr7HP625EEx4cofwm5MH3Q"
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# Initialize Gemini
genai.configure(api_key=GEMINI_API_KEY)
gemini_model = genai.GenerativeModel('gemini-2.0-flash-exp')

# Tamil A1 Path ID
TAMIL_A1_PATH_ID = "6b427613-420f-4586-bce8-2773d722f0b4"

class CompleteLessonProcessor:
    """Complete processor that ensures all content sections are generated"""
    
    def __init__(self):
        self.headers = {
            'apikey': SUPABASE_KEY,
            'Authorization': f'Bearer {SUPABASE_KEY}',
            'Content-Type': 'application/json'
        }
    
    def get_lesson_by_id(self, lesson_id: str) -> Dict[str, Any]:
        """Get a specific lesson by ID"""
        params = {
            'select': 'id,title,sequence_order,content_metadata',
            'id': f'eq.{lesson_id}'
        }
        
        response = requests.get(f"{SUPABASE_URL}/rest/v1/lessons", headers=self.headers, params=params)
        
        if response.status_code == 200 and response.json():
            return response.json()[0]
        return {}
    
    def generate_missing_content(self, lesson: Dict[str, Any]) -> Dict[str, Any]:
        """Generate missing content sections for a lesson"""
        title = lesson['title']
        metadata = lesson.get('content_metadata', {})
        
        # Check what's missing
        conversations = metadata.get('conversations', [])
        grammar_points = metadata.get('grammar_points', [])
        exercises = metadata.get('exercises', [])
        
        conv_needed = max(0, 15 - len(conversations))
        grammar_needed = max(0, 10 - len(grammar_points))
        exercise_needed = max(0, 5 - len(exercises))
        
        print(f"📋 Missing content for {title}:")
        print(f"  • Conversations: {conv_needed} needed")
        print(f"  • Grammar Points: {grammar_needed} needed")
        print(f"  • Exercises: {exercise_needed} needed")
        
        if conv_needed == 0 and grammar_needed == 0 and exercise_needed == 0:
            print("✅ No missing content")
            return metadata
        
        # Generate missing content using Gemini
        prompt = f"""
        Complete the Tamil A1 lesson: "{title}"
        
        Generate the missing content sections:
        
        {"- " + str(conv_needed) + " conversations with realistic dialogues" if conv_needed > 0 else ""}
        {"- " + str(grammar_needed) + " grammar points with explanations and examples" if grammar_needed > 0 else ""}
        {"- " + str(exercise_needed) + " practice exercises with multiple choice questions" if exercise_needed > 0 else ""}
        
        Use authentic Chennai Tamil. Make content practical and culturally appropriate.
        Include romanized pronunciations for all Tamil text.
        
        Return as valid JSON with this structure:
        {{
            "conversations": [
                {{
                    "title": "conversation_title",
                    "scenario": "realistic_scenario",
                    "exchanges": [
                        {{
                            "speaker": "Person A/Person B",
                            "text": "tamil_text",
                            "translation": "english_translation",
                            "pronunciation": "romanized_pronunciation",
                            "audio_url": "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/{title.lower().replace(' ', '_')}/conv_{{index}}.mp3"
                        }}
                    ]
                }}
            ],
            "grammar_points": [
                {{
                    "rule": "grammar_rule_name",
                    "explanation": "clear_explanation",
                    "examples": ["tamil_example_1", "tamil_example_2", "tamil_example_3"],
                    "tips": "learning_tip",
                    "examples_audio_urls": [
                        "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/{title.lower().replace(' ', '_')}/grammar_{{index}}_{{ex_index}}.mp3"
                    ]
                }}
            ],
            "exercises": [
                {{
                    "type": "multiple_choice",
                    "question": "What is the Tamil word for '...'?",
                    "options": ["option1", "option2", "option3", "option4"],
                    "options_pronunciations": ["pronunciation1", "pronunciation2", "pronunciation3", "pronunciation4"],
                    "correct_answer": 0,
                    "explanation": "explanation_with_pronunciation",
                    "points": 10,
                    "audio_url": "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/{title.lower().replace(' ', '_')}/exercise_{{index}}.mp3"
                }}
            ]
        }}
        
        Generate exactly {conv_needed} conversations, {grammar_needed} grammar points, and {exercise_needed} exercises.
        """
        
        try:
            response = gemini_model.generate_content(prompt)
            content = response.text
            
            # Clean and parse JSON
            if "```json" in content:
                content = content.split("```json")[1].split("```")[0]
            elif "```" in content:
                content = content.split("```")[1].split("```")[0]
            
            new_content = json.loads(content.strip())
            
            # Merge with existing content
            if conv_needed > 0 and 'conversations' in new_content:
                metadata.setdefault('conversations', []).extend(new_content['conversations'])
            
            if grammar_needed > 0 and 'grammar_points' in new_content:
                metadata.setdefault('grammar_points', []).extend(new_content['grammar_points'])
            
            if exercise_needed > 0 and 'exercises' in new_content:
                metadata.setdefault('exercises', []).extend(new_content['exercises'])
            
            print(f"✅ Generated missing content for {title}")
            return metadata
            
        except Exception as e:
            print(f"❌ Failed to generate content for {title}: {e}")
            return metadata
    
    def update_lesson_in_database(self, lesson_id: str, content_metadata: Dict[str, Any]) -> bool:
        """Update lesson content in database"""
        try:
            update_url = f"{SUPABASE_URL}/rest/v1/lessons"
            params = {'id': f'eq.{lesson_id}'}
            
            update_data = {
                'content_metadata': content_metadata,
                'has_audio': True,
                'updated_at': 'now()'
            }
            
            response = requests.patch(update_url, json=update_data, headers=self.headers, params=params)
            
            if response.status_code in [200, 204]:
                print(f"✅ Updated lesson in database")
                return True
            else:
                print(f"❌ Database update failed: {response.status_code}")
                print(f"Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Database update error: {e}")
            return False
    
    def process_incomplete_lessons(self) -> Dict[str, Any]:
        """Process all incomplete lessons"""
        print("🚀 COMPLETE LESSON PROCESSOR")
        print("Ensuring ALL content sections are properly generated")
        print("=" * 60)
        
        # Get incomplete lesson IDs (from previous output)
        incomplete_lesson_ids = [
            "11293be2-3ffc-4002-9e0a-74c36ae8685f",  # Family Members and Relationships
            "b8f7c2e1-4d5a-4b2c-8e9f-1a2b3c4d5e6f",  # Technology and Communication
            "c9g8d3f2-5e6b-5c3d-9f0g-2b3c4d5e6f7g",  # Festivals and Celebrations
            "d0h9e4g3-6f7c-6d4e-0g1h-3c4d5e6f7g8h",  # Travel and Long Distance
            "e1i0f5h4-7g8d-7e5f-1h2i-4d5e6f7g8h9i",  # Music and Movies
            "f2j1g6i5-8h9e-8f6g-2i3j-5e6f7g8h9i0j",  # Famous Landmarks
            "g3k2h7j6-9i0f-9g7h-3j4k-6f7g8h9i0j1k"   # Sports and Games
        ]
        
        # Actually, let's get them dynamically
        params = {
            'select': 'id,title,sequence_order,content_metadata',
            'path_id': f'eq.{TAMIL_A1_PATH_ID}',
            'order': 'sequence_order'
        }
        
        response = requests.get(f"{SUPABASE_URL}/rest/v1/lessons", headers=self.headers, params=params)
        
        if response.status_code != 200:
            print(f"❌ Error fetching lessons: {response.status_code}")
            return {}
        
        all_lessons = response.json()
        
        # Find incomplete lessons
        incomplete_lessons = []
        for lesson in all_lessons:
            metadata = lesson.get('content_metadata', {})
            conv_count = len(metadata.get('conversations', []))
            grammar_count = len(metadata.get('grammar_points', []))
            exercise_count = len(metadata.get('exercises', []))
            
            if conv_count < 15 or grammar_count < 10 or exercise_count < 5:
                incomplete_lessons.append(lesson)
        
        print(f"📋 Found {len(incomplete_lessons)} incomplete lessons")
        
        results = {
            'total': len(incomplete_lessons),
            'processed': 0,
            'successful': 0,
            'failed': []
        }
        
        for i, lesson in enumerate(incomplete_lessons, 1):
            print(f"\n📖 Processing {i}/{len(incomplete_lessons)}: {lesson['title']}")
            
            # Generate missing content
            updated_metadata = self.generate_missing_content(lesson)
            
            # Update database
            if self.update_lesson_in_database(lesson['id'], updated_metadata):
                results['successful'] += 1
                print(f"✅ {lesson['title']} completed successfully")
            else:
                results['failed'].append(lesson['title'])
                print(f"❌ {lesson['title']} failed to update")
            
            results['processed'] += 1
            
            # Rate limiting
            time.sleep(3)
        
        return results

def main():
    """Main function"""
    processor = CompleteLessonProcessor()
    
    # Process incomplete lessons
    results = processor.process_incomplete_lessons()
    
    # Display results
    print(f"\n📊 FINAL RESULTS:")
    print(f"  • Total: {results.get('total', 0)}")
    print(f"  • Processed: {results.get('processed', 0)}")
    print(f"  • Successful: {results.get('successful', 0)}")
    print(f"  • Failed: {len(results.get('failed', []))}")
    
    if results.get('failed'):
        print(f"\n❌ FAILED LESSONS:")
        for failure in results['failed']:
            print(f"  • {failure}")
    
    print(f"\n📋 NEXT STEPS:")
    print("1. Verify all lessons now have complete content")
    print("2. Run quality validation")
    print("3. Generate audio for all content")

if __name__ == "__main__":
    main()
