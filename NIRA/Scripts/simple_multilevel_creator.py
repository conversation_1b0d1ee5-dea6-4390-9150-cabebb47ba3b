#!/usr/bin/env python3
"""
Simple Multi-Level Creator for NIRA
Creates A2, B1, B2, C1, C2 lessons using the existing A1 path structure

This approach:
1. Uses the existing Tamil A1 path ID
2. Creates lessons with different difficulty levels (2-6)
3. Generates appropriate vocabulary for each level
4. Runs all levels in sequence
"""

import json
import requests
import time
from typing import Dict, List, Any
import google.generativeai as genai

# API Configuration
GEMINI_API_KEY = "AIzaSyAYasSocME9nPr7HP625EEx4cofwm5MH3Q"
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# Initialize Gemini
genai.configure(api_key=GEMINI_API_KEY)
gemini_model = genai.GenerativeModel('gemini-2.0-flash-exp')

# Use existing Tamil A1 path ID for all levels
TAMIL_PATH_ID = "6b427613-420f-4586-bce8-2773d722f0b4"

# Level configurations
LEVEL_CONFIG = {
    'A2': {
        'difficulty_level': 2,
        'vocab_count': 30,
        'duration': 45,
        'lessons': [
            "Advanced Greetings and Social Interactions",
            "Extended Family and Community Relations", 
            "Complex Numbers and Mathematical Concepts",
            "Advanced Colors and Detailed Descriptions",
            "Restaurant Dining and Food Culture"
        ]
    },
    'B1': {
        'difficulty_level': 3,
        'vocab_count': 35,
        'duration': 60,
        'lessons': [
            "Complex Social Situations and Etiquette",
            "Professional Networking and Business Relations",
            "Advanced Mathematical and Scientific Concepts",
            "Detailed Physical and Emotional Descriptions",
            "Fine Dining and Culinary Traditions"
        ]
    },
    'B2': {
        'difficulty_level': 4,
        'vocab_count': 40,
        'duration': 75,
        'lessons': [
            "Advanced Diplomatic and International Relations",
            "Corporate Strategy and Executive Leadership",
            "Quantum Physics and Advanced Mathematics",
            "Psychological Analysis and Behavioral Studies",
            "Molecular Gastronomy and Culinary Science"
        ]
    },
    'C1': {
        'difficulty_level': 5,
        'vocab_count': 45,
        'duration': 90,
        'lessons': [
            "Advanced Rhetorical Strategies and Persuasive Discourse",
            "Corporate Governance and Strategic Leadership Theory",
            "Theoretical Physics and Mathematical Modeling",
            "Cognitive Psychology and Neuroscientific Research",
            "Advanced Culinary Theory and Gastronomic Philosophy"
        ]
    },
    'C2': {
        'difficulty_level': 6,
        'vocab_count': 50,
        'duration': 120,
        'lessons': [
            "Classical Tamil Literature and Poetic Traditions",
            "Advanced Linguistic Theory and Tamil Grammar",
            "Tamil Philosophy and Ancient Wisdom Traditions",
            "Contemporary Tamil Literary Criticism",
            "Tamil Cultural Heritage and Historical Linguistics"
        ]
    }
}

class SimpleMultiLevelCreator:
    """Simple creator for all levels using existing path"""
    
    def __init__(self):
        self.headers = {
            'apikey': SUPABASE_KEY,
            'Authorization': f'Bearer {SUPABASE_KEY}',
            'Content-Type': 'application/json'
        }
    
    def generate_vocabulary(self, lesson_title: str, level: str, vocab_count: int) -> List[Dict[str, Any]]:
        """Generate vocabulary for specific level"""
        print(f"🔄 Generating {level} vocabulary for: {lesson_title}")
        
        level_descriptions = {
            'A2': 'elementary level with more complex vocabulary than A1',
            'B1': 'intermediate level with sophisticated vocabulary',
            'B2': 'upper-intermediate level with specialized vocabulary',
            'C1': 'advanced level with academic and professional vocabulary',
            'C2': 'mastery level with native-level and classical vocabulary'
        }
        
        prompt = f"""
        Generate exactly {vocab_count} authentic Tamil vocabulary items for the {level} {level_descriptions[level]} lesson: "{lesson_title}"

        Requirements:
        - {level} level difficulty
        - All words directly related to {lesson_title}
        - Use authentic Chennai Tamil
        - Each word must be unique
        - Include proper romanized pronunciation

        Return as valid JSON array:
        [
            {{
                "word": "authentic_tamil_word",
                "translation": "english_translation",
                "pronunciation": "romanized_pronunciation",
                "example": "tamil_example (pronunciation) - English translation",
                "difficulty": "{level.lower()}",
                "part_of_speech": "noun/verb/adjective",
                "word_audio_url": "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/{level.lower()}/{lesson_title.lower().replace(' ', '_')}/vocab_{{index:02d}}_word.mp3",
                "example_audio_url": "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/{level.lower()}/{lesson_title.lower().replace(' ', '_')}/vocab_{{index:02d}}_example.mp3"
            }}
        ]

        Generate exactly {vocab_count} items.
        """
        
        try:
            response = gemini_model.generate_content(prompt)
            content = response.text
            
            if "```json" in content:
                content = content.split("```json")[1].split("```")[0]
            elif "```" in content:
                content = content.split("```")[1].split("```")[0]
            
            vocabulary = json.loads(content.strip())
            
            if len(vocabulary) != vocab_count:
                vocabulary = vocabulary[:vocab_count] if len(vocabulary) > vocab_count else vocabulary + vocabulary[:vocab_count-len(vocabulary)]
            
            lesson_slug = lesson_title.lower().replace(' ', '_').replace('&', 'and')
            for i, vocab in enumerate(vocabulary, 1):
                vocab['word_audio_url'] = f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/{level.lower()}/{lesson_slug}/vocab_{i:02d}_word.mp3"
                vocab['example_audio_url'] = f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/{level.lower()}/{lesson_slug}/vocab_{i:02d}_example.mp3"
            
            print(f"✅ Generated {len(vocabulary)} {level} vocabulary items")
            return vocabulary
            
        except Exception as e:
            print(f"❌ Failed to generate vocabulary: {e}")
            return []
    
    def create_lesson(self, title: str, level: str, sequence: int) -> bool:
        """Create lesson for specific level"""
        print(f"\n🔧 CREATING {level} LESSON: {title}")
        
        config = LEVEL_CONFIG[level]
        vocabulary = self.generate_vocabulary(title, level, config['vocab_count'])
        
        if not vocabulary:
            print(f"❌ Failed to generate vocabulary for {title}")
            return False
        
        lesson_data = {
            'path_id': TAMIL_PATH_ID,
            'title': f"{title} ({level})",
            'description': f'{level} level Tamil lesson covering {title.lower()}',
            'lesson_type': 'comprehensive',
            'difficulty_level': config['difficulty_level'],
            'estimated_duration': config['duration'],
            'sequence_order': sequence + 1000 + (config['difficulty_level'] * 100),  # Offset to avoid conflicts
            'learning_objectives': [
                f'Master {level} vocabulary related to {title.lower()}',
                f'Apply {level} level grammar in {title.lower()} context'
            ],
            'vocabulary_focus': None,
            'grammar_concepts': [],
            'cultural_notes': '',
            'prerequisite_lessons': [],
            'content_metadata': {
                'title': f"{title} ({level})",
                'description': f'{level} level Tamil lesson covering {title.lower()}',
                'vocabulary': vocabulary,
                'conversations': [],
                'grammar_points': [],
                'exercises': [],
                'estimated_duration': config['duration'],
                'learning_objectives': [
                    f'Master {level} vocabulary related to {title.lower()}',
                    f'Apply {level} level grammar in {title.lower()} context'
                ]
            },
            'is_active': True,
            'audio_url': None,
            'audio_metadata': {},
            'has_audio': False
        }
        
        try:
            response = requests.post(f"{SUPABASE_URL}/rest/v1/lessons", json=lesson_data, headers=self.headers)
            
            if response.status_code == 201:
                print(f"✅ Created {level} lesson: {title}")
                return True
            else:
                print(f"❌ Database insert failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Error creating lesson: {e}")
            return False
    
    def create_all_levels(self) -> Dict[str, Any]:
        """Create lessons for all levels"""
        print("🚀 SIMPLE MULTI-LEVEL CREATOR")
        print("Creating A2, B1, B2, C1, C2 lessons")
        print("=" * 60)
        
        results = {}
        
        for level, config in LEVEL_CONFIG.items():
            print(f"\n📚 PROCESSING {level} LEVEL")
            print(f"Vocabulary: {config['vocab_count']} items, Duration: {config['duration']} min")
            print("-" * 40)
            
            level_results = {'created': 0, 'failed': []}
            
            for i, lesson_title in enumerate(config['lessons'], 1):
                print(f"\n📖 {level} {i}/{len(config['lessons'])}: {lesson_title}")
                
                if self.create_lesson(lesson_title, level, i):
                    level_results['created'] += 1
                else:
                    level_results['failed'].append(lesson_title)
                
                time.sleep(2)
            
            results[level] = level_results
        
        return results

def main():
    """Main function"""
    print("🎯 SIMPLE MULTI-LEVEL CREATOR")
    print("Creating Tamil lessons for A2, B1, B2, C1, C2")
    print("=" * 50)
    
    creator = SimpleMultiLevelCreator()
    
    # Create all levels
    results = creator.create_all_levels()
    
    # Display results
    print(f"\n📊 MULTI-LEVEL CREATION RESULTS:")
    print("=" * 50)
    
    total_created = 0
    total_failed = 0
    
    for level, level_results in results.items():
        created = level_results['created']
        failed = len(level_results['failed'])
        total_created += created
        total_failed += failed
        
        print(f"  • {level}: {created} created, {failed} failed")
    
    print(f"\n🎉 TOTAL: {total_created} lessons created, {total_failed} failed")
    
    if total_created > 0:
        print(f"\n✅ SUCCESS! Created {total_created} multi-level lessons")
        print(f"\n📋 NEXT STEPS:")
        print("1. ✅ Multi-level vocabulary complete")
        print("2. 🔄 Run conversation validators for each level")
        print("3. 🔄 Run grammar validators for each level")
        print("4. 🔄 Run exercises validators for each level")
        print("5. 🎵 Generate audio for all levels")

if __name__ == "__main__":
    main()
