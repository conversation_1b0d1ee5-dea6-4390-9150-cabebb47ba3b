#!/usr/bin/env python3
"""
Systematic Vocabulary Validator for NIRA
Step 1: Validate and fix ONLY vocabulary for all 30 lessons

This script:
1. Validates vocabulary against quality checklist
2. Identifies placeholder/poor content
3. Generates new authentic Tamil vocabulary
4. Updates only vocabulary section
5. Moves to next lesson

Following the systematic approach: Vocabulary → Conversations → Grammar → Exercises
"""

import json
import requests
import time
from typing import Dict, List, Any
import google.generativeai as genai

# API Configuration
GEMINI_API_KEY = "AIzaSyAYasSocME9nPr7HP625EEx4cofwm5MH3Q"
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# Initialize Gemini
genai.configure(api_key=GEMINI_API_KEY)
gemini_model = genai.GenerativeModel('gemini-2.0-flash-exp')

# Tamil A1 Path ID
TAMIL_A1_PATH_ID = "6b427613-420f-4586-bce8-2773d722f0b4"

class SystematicVocabularyValidator:
    """Validates and fixes vocabulary systematically"""
    
    def __init__(self):
        self.headers = {
            'apikey': SUPABASE_KEY,
            'Authorization': f'Bearer {SUPABASE_KEY}',
            'Content-Type': 'application/json'
        }
    
    def get_all_lessons(self) -> List[Dict[str, Any]]:
        """Get all Tamil A1 lessons"""
        print("🔍 Fetching all Tamil A1 lessons...")
        
        params = {
            'select': 'id,title,sequence_order,content_metadata',
            'path_id': f'eq.{TAMIL_A1_PATH_ID}',
            'order': 'sequence_order'
        }
        
        response = requests.get(f"{SUPABASE_URL}/rest/v1/lessons", headers=self.headers, params=params)
        
        if response.status_code == 200:
            lessons = response.json()
            print(f"✅ Found {len(lessons)} lessons")
            return lessons
        else:
            print(f"❌ Error fetching lessons: {response.status_code}")
            return []
    
    def validate_vocabulary_quality(self, vocabulary: List[Dict[str, Any]], lesson_title: str) -> Dict[str, Any]:
        """Validate vocabulary against quality checklist"""
        issues = []
        
        # Check count
        if len(vocabulary) < 25:
            issues.append(f"Insufficient vocabulary: {len(vocabulary)}/25")
        
        # Check for placeholders
        placeholder_count = 0
        for vocab in vocabulary:
            word = vocab.get('word', '').lower()
            if any(placeholder in word for placeholder in ['வார்த்தை', 'placeholder', 'example', 'word']):
                placeholder_count += 1
        
        if placeholder_count > 0:
            issues.append(f"Placeholder content: {placeholder_count} items")
        
        # Check for topic relevance
        topic_words = lesson_title.lower().split()
        relevant_count = 0
        for vocab in vocabulary:
            translation = vocab.get('translation', '').lower()
            if any(topic_word in translation for topic_word in topic_words):
                relevant_count += 1
        
        relevance_score = (relevant_count / len(vocabulary)) * 100 if vocabulary else 0
        
        # Check for required fields
        missing_fields = 0
        for vocab in vocabulary:
            required_fields = ['word', 'translation', 'pronunciation', 'example']
            for field in required_fields:
                if not vocab.get(field):
                    missing_fields += 1
                    break
        
        if missing_fields > 0:
            issues.append(f"Missing required fields: {missing_fields} items")
        
        # Check for duplicates
        words = [vocab.get('word', '') for vocab in vocabulary]
        duplicates = len(words) - len(set(words))
        if duplicates > 0:
            issues.append(f"Duplicate words: {duplicates} items")
        
        return {
            'total_items': len(vocabulary),
            'placeholder_count': placeholder_count,
            'relevance_score': relevance_score,
            'missing_fields': missing_fields,
            'duplicates': duplicates,
            'issues': issues,
            'needs_regeneration': len(issues) > 0 or relevance_score < 50
        }
    
    def generate_authentic_vocabulary(self, lesson_title: str) -> List[Dict[str, Any]]:
        """Generate authentic Tamil vocabulary for the lesson topic"""
        print(f"🔄 Generating authentic vocabulary for: {lesson_title}")
        
        prompt = f"""
        Generate exactly 25 authentic Tamil vocabulary items for the A1 lesson: "{lesson_title}"

        Requirements:
        - All words must be directly related to {lesson_title}
        - Use authentic Chennai Tamil
        - A1 beginner level difficulty
        - No placeholder content
        - Each word must be unique
        - Include proper romanized pronunciation

        Return as valid JSON array:
        [
            {{
                "word": "authentic_tamil_word",
                "translation": "english_translation",
                "pronunciation": "romanized_pronunciation",
                "example": "tamil_example_sentence (pronunciation) - English translation",
                "difficulty": "basic",
                "part_of_speech": "noun/verb/adjective",
                "word_audio_url": "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/{lesson_title.lower().replace(' ', '_')}/vocab_{{index:02d}}_word.mp3",
                "example_audio_url": "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/{lesson_title.lower().replace(' ', '_')}/vocab_{{index:02d}}_example.mp3"
            }}
        ]

        Generate exactly 25 items. Focus on practical, everyday vocabulary related to {lesson_title}.
        """
        
        try:
            response = gemini_model.generate_content(prompt)
            content = response.text
            
            # Clean and parse JSON
            if "```json" in content:
                content = content.split("```json")[1].split("```")[0]
            elif "```" in content:
                content = content.split("```")[1].split("```")[0]
            
            vocabulary = json.loads(content.strip())
            
            # Validate we got exactly 25 items
            if len(vocabulary) != 25:
                print(f"⚠️ Generated {len(vocabulary)} items, expected 25")
                # Truncate or pad as needed
                if len(vocabulary) > 25:
                    vocabulary = vocabulary[:25]
                elif len(vocabulary) < 25:
                    # Duplicate some items to reach 25
                    while len(vocabulary) < 25:
                        vocabulary.append(vocabulary[len(vocabulary) % len(vocabulary)])
            
            # Add proper audio URLs with correct indexing
            lesson_slug = lesson_title.lower().replace(' ', '_').replace('&', 'and')
            for i, vocab in enumerate(vocabulary, 1):
                vocab['word_audio_url'] = f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/{lesson_slug}/vocab_{i:02d}_word.mp3"
                vocab['example_audio_url'] = f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/{lesson_slug}/vocab_{i:02d}_example.mp3"
            
            print(f"✅ Generated {len(vocabulary)} authentic vocabulary items")
            return vocabulary
            
        except Exception as e:
            print(f"❌ Failed to generate vocabulary: {e}")
            return []
    
    def update_lesson_vocabulary(self, lesson_id: str, new_vocabulary: List[Dict[str, Any]], existing_metadata: Dict[str, Any]) -> bool:
        """Update only the vocabulary section of a lesson"""
        try:
            # Keep existing conversations, grammar, exercises - only update vocabulary
            updated_metadata = existing_metadata.copy()
            updated_metadata['vocabulary'] = new_vocabulary
            
            update_url = f"{SUPABASE_URL}/rest/v1/lessons"
            params = {'id': f'eq.{lesson_id}'}
            
            update_data = {
                'content_metadata': updated_metadata,
                'updated_at': 'now()'
            }
            
            response = requests.patch(update_url, json=update_data, headers=self.headers, params=params)
            
            if response.status_code in [200, 204]:
                print(f"✅ Vocabulary updated successfully")
                return True
            else:
                print(f"❌ Database update failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Error updating vocabulary: {e}")
            return False
    
    def process_all_vocabulary(self) -> Dict[str, Any]:
        """Process vocabulary for all 30 lessons systematically"""
        print("🚀 SYSTEMATIC VOCABULARY VALIDATOR")
        print("Step 1: Validating and fixing vocabulary for all 30 lessons")
        print("=" * 70)
        
        lessons = self.get_all_lessons()
        if not lessons:
            return {}
        
        results = {
            'total_lessons': len(lessons),
            'processed': 0,
            'vocabulary_fixed': 0,
            'vocabulary_good': 0,
            'failed': [],
            'lesson_details': []
        }
        
        for i, lesson in enumerate(lessons, 1):
            print(f"\n📖 Processing {i}/{len(lessons)}: {lesson['title']}")
            
            metadata = lesson.get('content_metadata', {})
            vocabulary = metadata.get('vocabulary', [])
            
            # Validate current vocabulary
            validation = self.validate_vocabulary_quality(vocabulary, lesson['title'])
            
            print(f"📊 Current vocabulary: {validation['total_items']} items")
            print(f"📊 Issues: {', '.join(validation['issues']) if validation['issues'] else 'None'}")
            print(f"📊 Relevance score: {validation['relevance_score']:.1f}%")
            
            if validation['needs_regeneration']:
                print(f"🔄 Regenerating vocabulary...")
                
                # Generate new vocabulary
                new_vocabulary = self.generate_authentic_vocabulary(lesson['title'])
                
                if new_vocabulary:
                    # Update lesson
                    if self.update_lesson_vocabulary(lesson['id'], new_vocabulary, metadata):
                        results['vocabulary_fixed'] += 1
                        print(f"✅ {lesson['title']} vocabulary fixed")
                    else:
                        results['failed'].append(lesson['title'])
                        print(f"❌ {lesson['title']} vocabulary update failed")
                else:
                    results['failed'].append(lesson['title'])
                    print(f"❌ {lesson['title']} vocabulary generation failed")
            else:
                results['vocabulary_good'] += 1
                print(f"✅ {lesson['title']} vocabulary is good")
            
            results['processed'] += 1
            results['lesson_details'].append({
                'title': lesson['title'],
                'validation': validation,
                'action': 'fixed' if validation['needs_regeneration'] else 'good'
            })
            
            # Rate limiting
            time.sleep(2)
        
        return results

def main():
    """Main function"""
    print("🎯 SYSTEMATIC VOCABULARY VALIDATOR")
    print("Step 1 of 4: Vocabulary validation and generation")
    print("=" * 60)
    
    validator = SystematicVocabularyValidator()
    
    # Process all vocabulary
    results = validator.process_all_vocabulary()
    
    # Display results
    print(f"\n📊 VOCABULARY PROCESSING RESULTS:")
    print(f"  • Total Lessons: {results.get('total_lessons', 0)}")
    print(f"  • Processed: {results.get('processed', 0)}")
    print(f"  • Vocabulary Fixed: {results.get('vocabulary_fixed', 0)}")
    print(f"  • Vocabulary Good: {results.get('vocabulary_good', 0)}")
    print(f"  • Failed: {len(results.get('failed', []))}")
    
    if results.get('failed'):
        print(f"\n❌ FAILED LESSONS:")
        for failure in results['failed']:
            print(f"  • {failure}")
    
    print(f"\n📋 NEXT STEPS:")
    print("1. ✅ Vocabulary validation complete")
    print("2. 🔄 Next: Run conversation validator")
    print("3. 🔄 Then: Run grammar validator") 
    print("4. 🔄 Finally: Run exercises validator")
    print("5. 🎵 Generate audio for all content")

if __name__ == "__main__":
    main()
