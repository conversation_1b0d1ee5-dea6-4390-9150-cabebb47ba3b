#!/usr/bin/env python3
"""
Add romanized Tamil pronunciations to ALL exercise options
"""

import requests
import json

def create_exercises_with_pronunciations():
    """Create exercises with romanized Tamil for ALL options"""
    
    lesson_slug = "directions_and_locations"
    base_url = f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/{lesson_slug}"
    
    exercises = [
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "What is the Tamil word for 'north'?",
            "options": ["வடக்கு", "தெற்கு", "கிழக்கு", "மேற்கு"],
            "options_pronunciations": ["vadakku", "therku", "kizhakku", "merku"],
            "correctAnswer": 0,
            "explanation": "வடக்கு (vadakku) means north in Tamil",
            "options_audio_urls": [f"{base_url}/exercise_01_option_01.mp3", f"{base_url}/exercise_01_option_02.mp3", f"{base_url}/exercise_01_option_03.mp3", f"{base_url}/exercise_01_option_04.mp3"]
        },
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "How do you say 'turn left' in Tamil?",
            "options": ["வலது பக்கம் திரும்புங்கள்", "இடது பக்கம் திரும்புங்கள்", "நேராக போங்கள்", "பின்னால் போங்கள்"],
            "options_pronunciations": ["valathu pakkam thirumbungal", "idathu pakkam thirumbungal", "neraaga pongal", "pinnaal pongal"],
            "correctAnswer": 1,
            "explanation": "இடது பக்கம் திரும்புங்கள் (idathu pakkam thirumbungal) means turn left",
            "options_audio_urls": [f"{base_url}/exercise_02_option_01.mp3", f"{base_url}/exercise_02_option_02.mp3", f"{base_url}/exercise_02_option_03.mp3", f"{base_url}/exercise_02_option_04.mp3"]
        },
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "What does 'சாலை' mean?",
            "options": ["சாலை", "தெரு", "பாலம்", "கட்டிடம்"],
            "options_pronunciations": ["saalai", "theru", "paalam", "kattidam"],
            "correctAnswer": 0,
            "explanation": "சாலை (saalai) means road in Tamil",
            "options_audio_urls": [f"{base_url}/exercise_03_option_01.mp3", f"{base_url}/exercise_03_option_02.mp3", f"{base_url}/exercise_03_option_03.mp3", f"{base_url}/exercise_03_option_04.mp3"]
        },
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "Which word means 'east' in Tamil?",
            "options": ["வடக்கு", "தெற்கு", "கிழக்கு", "மேற்கு"],
            "options_pronunciations": ["vadakku", "therku", "kizhakku", "merku"],
            "correctAnswer": 2,
            "explanation": "கிழக்கு (kizhakku) means east in Tamil",
            "options_audio_urls": [f"{base_url}/exercise_04_option_01.mp3", f"{base_url}/exercise_04_option_02.mp3", f"{base_url}/exercise_04_option_03.mp3", f"{base_url}/exercise_04_option_04.mp3"]
        },
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "How do you ask 'Where is it?' in Tamil?",
            "options": ["எங்கே இருக்கிறது?", "என்ன இருக்கிறது?", "எப்போது இருக்கிறது?", "எப்படி இருக்கிறது?"],
            "options_pronunciations": ["engE irukkiRadhu?", "enna irukkiRadhu?", "eppodhu irukkiRadhu?", "eppaddi irukkiRadhu?"],
            "correctAnswer": 0,
            "explanation": "எங்கே இருக்கிறது? (engE irukkiRadhu?) means where is it?",
            "options_audio_urls": [f"{base_url}/exercise_05_option_01.mp3", f"{base_url}/exercise_05_option_02.mp3", f"{base_url}/exercise_05_option_03.mp3", f"{base_url}/exercise_05_option_04.mp3"]
        },
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "What does 'அருகில்' mean?",
            "options": ["அருகில்", "தூரம்", "எதிரில்", "பின்"],
            "options_pronunciations": ["arugil", "thoorem", "edhiril", "pin"],
            "correctAnswer": 0,
            "explanation": "அருகில் (arugil) means near in Tamil",
            "options_audio_urls": [f"{base_url}/exercise_06_option_01.mp3", f"{base_url}/exercise_06_option_02.mp3", f"{base_url}/exercise_06_option_03.mp3", f"{base_url}/exercise_06_option_04.mp3"]
        },
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "Which word means 'straight' in Tamil?",
            "options": ["இடது", "வலது", "நேராக", "திரும்பு"],
            "options_pronunciations": ["idathu", "valathu", "neraaga", "thirambu"],
            "correctAnswer": 2,
            "explanation": "நேராக (neraaga) means straight in Tamil",
            "options_audio_urls": [f"{base_url}/exercise_07_option_01.mp3", f"{base_url}/exercise_07_option_02.mp3", f"{base_url}/exercise_07_option_03.mp3", f"{base_url}/exercise_07_option_04.mp3"]
        },
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "What does 'பாலம்' mean?",
            "options": ["பாலம்", "சாலை", "தெரு", "கட்டிடம்"],
            "options_pronunciations": ["paalam", "saalai", "theru", "kattidam"],
            "correctAnswer": 0,
            "explanation": "பாலம் (paalam) means bridge in Tamil",
            "options_audio_urls": [f"{base_url}/exercise_08_option_01.mp3", f"{base_url}/exercise_08_option_02.mp3", f"{base_url}/exercise_08_option_03.mp3", f"{base_url}/exercise_08_option_04.mp3"]
        },
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "How do you say 'behind' in Tamil?",
            "options": ["முன்", "பின்", "மேல்", "கீழ்"],
            "options_pronunciations": ["mun", "pin", "mel", "keezh"],
            "correctAnswer": 1,
            "explanation": "பின் (pin) means behind in Tamil",
            "options_audio_urls": [f"{base_url}/exercise_09_option_01.mp3", f"{base_url}/exercise_09_option_02.mp3", f"{base_url}/exercise_09_option_03.mp3", f"{base_url}/exercise_09_option_04.mp3"]
        },
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "What does 'முக்கோணம்' mean?",
            "options": ["முக்கோணம்", "மூலை", "நடு", "எதிர்"],
            "options_pronunciations": ["mukkonam", "moolai", "nadu", "edhir"],
            "correctAnswer": 0,
            "explanation": "முக்கோணம் (mukkonam) means junction in Tamil",
            "options_audio_urls": [f"{base_url}/exercise_10_option_01.mp3", f"{base_url}/exercise_10_option_02.mp3", f"{base_url}/exercise_10_option_03.mp3", f"{base_url}/exercise_10_option_04.mp3"]
        },
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "Which word means 'distance' in Tamil?",
            "options": ["இடம்", "வழி", "தூரம்", "திசை"],
            "options_pronunciations": ["idam", "vazhi", "thoorem", "thisai"],
            "correctAnswer": 2,
            "explanation": "தூரம் (thoorem) means distance in Tamil",
            "options_audio_urls": [f"{base_url}/exercise_11_option_01.mp3", f"{base_url}/exercise_11_option_02.mp3", f"{base_url}/exercise_11_option_03.mp3", f"{base_url}/exercise_11_option_04.mp3"]
        },
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "How do you say 'opposite' in Tamil?",
            "options": ["அருகில்", "எதிர்", "பின்", "முன்"],
            "options_pronunciations": ["arugil", "edhir", "pin", "mun"],
            "correctAnswer": 1,
            "explanation": "எதிர் (edhir) means opposite in Tamil",
            "options_audio_urls": [f"{base_url}/exercise_12_option_01.mp3", f"{base_url}/exercise_12_option_02.mp3", f"{base_url}/exercise_12_option_03.mp3", f"{base_url}/exercise_12_option_04.mp3"]
        }
    ]
    
    return exercises

def update_lesson_exercises():
    """Update lesson with exercises that have option pronunciations"""
    
    # Get current lesson data
    lesson_id = "3318f37a-adef-4dd2-9f7a-6f8a2618cd38"
    
    # Get current lesson
    url = f"https://lyaojebttnqilmdosmjk.supabase.co/rest/v1/lessons?id=eq.{lesson_id}&select=content_metadata"
    headers = {
        "apikey": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c",
        "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"
    }
    
    response = requests.get(url, headers=headers)
    if response.status_code != 200:
        print(f"❌ Failed to get lesson: {response.status_code}")
        return False
    
    lesson_data = response.json()[0]
    content_metadata = lesson_data["content_metadata"]
    
    # Update exercises with pronunciations
    content_metadata["exercises"] = create_exercises_with_pronunciations()
    
    # Update lesson
    update_url = f"https://lyaojebttnqilmdosmjk.supabase.co/rest/v1/lessons?id=eq.{lesson_id}"
    update_headers = {
        **headers,
        "Content-Type": "application/json",
        "Prefer": "return=minimal"
    }
    
    update_data = {"content_metadata": content_metadata}
    update_response = requests.patch(update_url, headers=update_headers, json=update_data)
    
    return update_response.status_code == 204

if __name__ == "__main__":
    print("🎯 Adding romanized Tamil pronunciations to ALL exercise options...")
    
    exercises = create_exercises_with_pronunciations()
    print(f"✅ Created {len(exercises)} exercises with option pronunciations")
    
    print("🔄 Updating lesson in database...")
    success = update_lesson_exercises()
    
    if success:
        print("✅ SUCCESS: Exercise options now have romanized Tamil!")
        print("🎉 ALL exercise options show pronunciation in brackets!")
        print("🎯 Ready for testing in the app!")
    else:
        print("❌ FAILED: Could not update lesson exercises")
