#!/usr/bin/env python3
"""
Fix Body Parts lesson - remove duplicate exercises and conversations
"""

import requests
import json

def fix_lesson():
    # Get current lesson
    url = "https://lyaojebttnqilmdosmjk.supabase.co/rest/v1/lessons?title=eq.Body%20Parts%20and%20Health&select=id,content_metadata"
    headers = {
        "apikey": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c",
        "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"
    }
    
    response = requests.get(url, headers=headers)
    data = response.json()[0]
    lesson_id = data['id']
    content = data['content_metadata']
    
    # Remove duplicate exercises - keep only first 12 unique ones
    exercises = content['exercises']
    unique_exercises = []
    seen_questions = set()
    
    for ex in exercises:
        if ex['question'] not in seen_questions:
            unique_exercises.append(ex)
            seen_questions.add(ex['question'])
        if len(unique_exercises) >= 12:
            break
    
    content['exercises'] = unique_exercises
    
    # Remove duplicate conversations - keep only first 15 unique ones
    conversations = content['conversations']
    unique_conversations = []
    seen_titles = set()
    
    for conv in conversations:
        if conv['title'] not in seen_titles:
            unique_conversations.append(conv)
            seen_titles.add(conv['title'])
        if len(unique_conversations) >= 15:
            break
    
    content['conversations'] = unique_conversations
    
    # Update lesson
    update_url = f"https://lyaojebttnqilmdosmjk.supabase.co/rest/v1/lessons?id=eq.{lesson_id}"
    update_headers = {**headers, "Content-Type": "application/json", "Prefer": "return=minimal"}
    
    response = requests.patch(update_url, headers=update_headers, json={"content_metadata": content})
    
    print(f"✅ Fixed lesson - {len(unique_exercises)} unique exercises, {len(unique_conversations)} unique conversations")
    return response.status_code == 204

if __name__ == "__main__":
    fix_lesson()
