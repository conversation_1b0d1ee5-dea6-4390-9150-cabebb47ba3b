#!/usr/bin/env python3
"""
C1 Vocabulary Validator for NIRA
C1 Advanced level Tamil lessons

This script creates and validates C1 advanced level vocabulary:
- 45 vocabulary items per lesson
- Highly sophisticated vocabulary
- Academic and professional terminology
- Advanced level difficulty
"""

import json
import requests
import time
from typing import Dict, List, Any
import google.generativeai as genai

# API Configuration
GEMINI_API_KEY = "AIzaSyAYasSocME9nPr7HP625EEx4cofwm5MH3Q"
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# Initialize Gemini
genai.configure(api_key=GEMINI_API_KEY)
gemini_model = genai.GenerativeModel('gemini-2.0-flash-exp')

# Tamil C1 Path ID
TAMIL_C1_PATH_ID = "c1-advanced-path-id"

# C1 Advanced Tamil Lessons (30 lessons)
C1_LESSONS = [
    "Advanced Rhetorical Strategies and Persuasive Discourse",
    "Corporate Governance and Strategic Leadership Theory",
    "Theoretical Physics and Mathematical Modeling",
    "Cognitive Psychology and Neuroscientific Research",
    "Advanced Culinary Theory and Gastronomic Philosophy",
    "Medical Ethics and Bioethical Decision Making",
    "Environmental Policy and Sustainable Development Theory",
    "Aerospace Technology and Space Exploration Ethics",
    "Fashion Theory and Cultural Semiotics",
    "Architectural Philosophy and Design Theory",
    "Existential Philosophy and Metaphysical Inquiry",
    "International Finance and Global Economic Theory",
    "Organizational Psychology and Change Management",
    "Behavioral Economics and Market Psychology",
    "Urban Sociology and Metropolitan Development",
    "Crisis Communication and Strategic Risk Management",
    "Film Criticism and Cinematic Theory",
    "Executive Leadership and Transformational Management",
    "Academic Discourse and Scholarly Methodology",
    "Computational Linguistics and Natural Language Processing",
    "Jurisprudence and Legal Philosophy",
    "Cultural Anthropology and Ethnographic Studies",
    "Advanced Energy Systems and Technological Innovation",
    "Culinary Arts and Food Culture Theory",
    "Historical Methodology and Historiographical Analysis",
    "Transportation Policy and Infrastructure Development",
    "International Relations and Diplomatic Theory",
    "Aesthetic Theory and Contemporary Art Criticism",
    "Cultural Heritage and Preservation Ethics",
    "Sports Psychology and Performance Optimization"
]

class C1VocabularyValidator:
    """C1 vocabulary validator and creator"""
    
    def __init__(self):
        self.headers = {
            'apikey': SUPABASE_KEY,
            'Authorization': f'Bearer {SUPABASE_KEY}',
            'Content-Type': 'application/json'
        }
    
    def create_c1_lesson_with_vocabulary(self, title: str, sequence: int) -> bool:
        """Create C1 lesson with vocabulary directly"""
        print(f"\n🔧 CREATING C1 LESSON: {title}")
        
        vocabulary = self.generate_c1_vocabulary(title)
        
        if not vocabulary:
            print(f"❌ Failed to generate vocabulary for {title}")
            return False
        
        lesson_data = {
            'path_id': TAMIL_C1_PATH_ID,
            'title': title,
            'description': f'Advanced Tamil lesson covering {title.lower()} with sophisticated vocabulary and complex theoretical concepts',
            'lesson_type': 'comprehensive',
            'difficulty_level': 5,  # C1 level
            'estimated_duration': 90,
            'sequence_order': sequence,
            'learning_objectives': [
                f'Master advanced vocabulary in {title.lower()}',
                f'Engage in sophisticated academic discourse about {title.lower()}',
                f'Apply complex theoretical frameworks in {title.lower()} context',
                f'Synthesize and evaluate {title.lower()} concepts at advanced level'
            ],
            'vocabulary_focus': None,
            'grammar_concepts': [],
            'cultural_notes': '',
            'prerequisite_lessons': [],
            'content_metadata': {
                'title': title,
                'description': f'Advanced Tamil lesson covering {title.lower()}',
                'vocabulary': vocabulary,
                'conversations': [],
                'grammar_points': [],
                'exercises': [],
                'estimated_duration': 90,
                'learning_objectives': [
                    f'Master advanced vocabulary in {title.lower()}',
                    f'Engage in sophisticated academic discourse about {title.lower()}',
                    f'Apply complex theoretical frameworks in {title.lower()} context',
                    f'Synthesize and evaluate {title.lower()} concepts at advanced level'
                ]
            },
            'is_active': True,
            'audio_url': None,
            'audio_metadata': {},
            'has_audio': False
        }
        
        try:
            response = requests.post(f"{SUPABASE_URL}/rest/v1/lessons", json=lesson_data, headers=self.headers)
            
            if response.status_code == 201:
                print(f"✅ Created C1 lesson: {title}")
                print(f"📊 Generated: {len(vocabulary)} vocabulary items")
                return True
            else:
                print(f"❌ Database insert failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Error creating lesson: {e}")
            return False
    
    def generate_c1_vocabulary(self, lesson_title: str) -> List[Dict[str, Any]]:
        """Generate C1 level Tamil vocabulary"""
        print(f"🔄 Generating C1 vocabulary for: {lesson_title}")
        
        prompt = f"""
        Generate exactly 45 authentic Tamil vocabulary items for the C1 advanced lesson: "{lesson_title}"

        Requirements:
        - C1 advanced level difficulty
        - Highly sophisticated and academic vocabulary
        - Theoretical and abstract concepts
        - Professional and scholarly terminology
        - Complex philosophical and technical terms
        - Use authentic Chennai Tamil with classical influences
        - Each word must be unique

        Return as valid JSON array with 45 items for advanced complexity.
        """
        
        try:
            response = gemini_model.generate_content(prompt)
            content = response.text
            
            if "```json" in content:
                content = content.split("```json")[1].split("```")[0]
            elif "```" in content:
                content = content.split("```")[1].split("```")[0]
            
            vocabulary = json.loads(content.strip())
            
            if len(vocabulary) != 45:
                vocabulary = vocabulary[:45] if len(vocabulary) > 45 else vocabulary + vocabulary[:45-len(vocabulary)]
            
            lesson_slug = lesson_title.lower().replace(' ', '_').replace('&', 'and')
            for i, vocab in enumerate(vocabulary, 1):
                vocab['word_audio_url'] = f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/c1/{lesson_slug}/vocab_{i:02d}_word.mp3"
                vocab['example_audio_url'] = f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/c1/{lesson_slug}/vocab_{i:02d}_example.mp3"
            
            print(f"✅ Generated {len(vocabulary)} C1 vocabulary items")
            return vocabulary
            
        except Exception as e:
            print(f"❌ Failed to generate vocabulary: {e}")
            return []
    
    def create_all_c1_lessons(self) -> Dict[str, Any]:
        """Create all 30 C1 lessons with vocabulary"""
        print("🚀 C1 ADVANCED VOCABULARY VALIDATOR")
        print("Creating 30 advanced Tamil lessons")
        print("=" * 60)
        
        results = {'total_lessons': len(C1_LESSONS), 'created': 0, 'failed': []}
        
        for i, lesson_title in enumerate(C1_LESSONS, 1):
            print(f"\n📖 Processing {i}/{len(C1_LESSONS)}: {lesson_title}")
            
            if self.create_c1_lesson_with_vocabulary(lesson_title, i):
                results['created'] += 1
            else:
                results['failed'].append(lesson_title)
            
            time.sleep(2)
        
        return results

def main():
    validator = C1VocabularyValidator()
    results = validator.create_all_c1_lessons()
    
    print(f"\n📊 C1 RESULTS: Created {results['created']}/{results['total_lessons']} lessons")
    if results['failed']:
        print(f"❌ Failed: {len(results['failed'])} lessons")

if __name__ == "__main__":
    main()
