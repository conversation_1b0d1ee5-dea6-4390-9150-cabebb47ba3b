#!/usr/bin/env python3
"""
Create PERFECT Basic Greetings and Introductions Lesson
Following Complete_Lesson_Implementation_Guide.md and COMPREHENSIVE_QUALITY_CHECKLIST.md
"""

import requests
import json

def create_perfect_greetings_lesson():
    """Create Basic Greetings and Introductions lesson with ALL quality standards"""
    
    lesson_id = "342230c2-8ea9-495d-bbef-ab0bec4df7be"
    lesson_slug = "basic_greetings_and_introductions"
    base_url = f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/{lesson_slug}"
    
    # ✅ VOCABULARY - 25 unique items for greetings and introductions
    vocabulary = [
        {
            "word": "வணக்கம்",
            "translation": "Hello/Goodbye",
            "pronunciation": "vanakkam",
            "example": "வணக்கம்! நான் ராம் (vanakkam! naan raam) - Hello! I am Ram",
            "difficulty": "basic",
            "part_of_speech": "interjection",
            "word_audio_url": f"{base_url}/vocab_01_word.mp3",
            "example_audio_url": f"{base_url}/vocab_01_example.mp3"
        },
        {
            "word": "பெயர்",
            "translation": "Name",
            "pronunciation": "peyar",
            "example": "என் பெயர் சுமதி (en peyar sumadhi) - My name is Sumathi",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_02_word.mp3",
            "example_audio_url": f"{base_url}/vocab_02_example.mp3"
        },
        {
            "word": "நன்றி",
            "translation": "Thank you",
            "pronunciation": "nandri",
            "example": "உங்கள் உதவிக்கு நன்றி (ungal udhavikku nandri) - Thank you for your help",
            "difficulty": "basic",
            "part_of_speech": "interjection",
            "word_audio_url": f"{base_url}/vocab_03_word.mp3",
            "example_audio_url": f"{base_url}/vocab_03_example.mp3"
        },
        {
            "word": "மன்னிக்கவும்",
            "translation": "Sorry/Excuse me",
            "pronunciation": "mannikkavum",
            "example": "மன்னிக்கவும், நேரம் என்ன? (mannikkavum, neram enna?) - Excuse me, what time is it?",
            "difficulty": "basic",
            "part_of_speech": "interjection",
            "word_audio_url": f"{base_url}/vocab_04_word.mp3",
            "example_audio_url": f"{base_url}/vocab_04_example.mp3"
        },
        {
            "word": "தயவு செய்து",
            "translation": "Please",
            "pronunciation": "dhayavu seydhu",
            "example": "தயவு செய்து உட்காருங்கள் (dhayavu seydhu udkaarungal) - Please sit down",
            "difficulty": "basic",
            "part_of_speech": "phrase",
            "word_audio_url": f"{base_url}/vocab_05_word.mp3",
            "example_audio_url": f"{base_url}/vocab_05_example.mp3"
        },
        {
            "word": "நான்",
            "translation": "I",
            "pronunciation": "naan",
            "example": "நான் மாணவன் (naan maanavan) - I am a student",
            "difficulty": "basic",
            "part_of_speech": "pronoun",
            "word_audio_url": f"{base_url}/vocab_06_word.mp3",
            "example_audio_url": f"{base_url}/vocab_06_example.mp3"
        },
        {
            "word": "நீங்கள்",
            "translation": "You (formal)",
            "pronunciation": "neengal",
            "example": "நீங்கள் எங்கிருந்து வருகிறீர்கள்? (neengal engirundu varukiReerkal?) - Where are you coming from?",
            "difficulty": "basic",
            "part_of_speech": "pronoun",
            "word_audio_url": f"{base_url}/vocab_07_word.mp3",
            "example_audio_url": f"{base_url}/vocab_07_example.mp3"
        },
        {
            "word": "நீ",
            "translation": "You (informal)",
            "pronunciation": "nee",
            "example": "நீ எப்படி இருக்கிறாய்? (nee eppaddi irukkiRaay?) - How are you? (informal)",
            "difficulty": "basic",
            "part_of_speech": "pronoun",
            "word_audio_url": f"{base_url}/vocab_08_word.mp3",
            "example_audio_url": f"{base_url}/vocab_08_example.mp3"
        },
        {
            "word": "காலை வணக்கம்",
            "translation": "Good morning",
            "pronunciation": "kaalai vanakkam",
            "example": "காலை வணக்கம் அம்மா (kaalai vanakkam amma) - Good morning, mother",
            "difficulty": "basic",
            "part_of_speech": "phrase",
            "word_audio_url": f"{base_url}/vocab_09_word.mp3",
            "example_audio_url": f"{base_url}/vocab_09_example.mp3"
        },
        {
            "word": "மாலை வணக்கம்",
            "translation": "Good evening",
            "pronunciation": "maalai vanakkam",
            "example": "மாலை வணக்கம் ஐயா (maalai vanakkam aiyyaa) - Good evening, sir",
            "difficulty": "basic",
            "part_of_speech": "phrase",
            "word_audio_url": f"{base_url}/vocab_10_word.mp3",
            "example_audio_url": f"{base_url}/vocab_10_example.mp3"
        },
        {
            "word": "எப்படி இருக்கிறீர்கள்?",
            "translation": "How are you?",
            "pronunciation": "eppaddi irukkiReerkal?",
            "example": "வணக்கம்! எப்படி இருக்கிறீர்கள்? (vanakkam! eppaddi irukkiReerkal?) - Hello! How are you?",
            "difficulty": "basic",
            "part_of_speech": "phrase",
            "word_audio_url": f"{base_url}/vocab_11_word.mp3",
            "example_audio_url": f"{base_url}/vocab_11_example.mp3"
        },
        {
            "word": "நல்லா இருக்கேன்",
            "translation": "I am fine",
            "pronunciation": "nallaa irukken",
            "example": "நான் நல்லா இருக்கேன், நன்றி (naan nallaa irukken, nandri) - I am fine, thank you",
            "difficulty": "basic",
            "part_of_speech": "phrase",
            "word_audio_url": f"{base_url}/vocab_12_word.mp3",
            "example_audio_url": f"{base_url}/vocab_12_example.mp3"
        },
        {
            "word": "சந்தோஷம்",
            "translation": "Happy/Pleasure",
            "pronunciation": "sandhosham",
            "example": "உங்களை சந்தித்ததில் சந்தோஷம் (ungalai sandhiththadhil sandhosham) - Happy to meet you",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_13_word.mp3",
            "example_audio_url": f"{base_url}/vocab_13_example.mp3"
        },
        {
            "word": "வாங்க",
            "translation": "Come (invitation)",
            "pronunciation": "vaanga",
            "example": "உள்ளே வாங்க (ullE vaanga) - Come inside",
            "difficulty": "basic",
            "part_of_speech": "verb",
            "word_audio_url": f"{base_url}/vocab_14_word.mp3",
            "example_audio_url": f"{base_url}/vocab_14_example.mp3"
        },
        {
            "word": "போங்க",
            "translation": "Go (polite)",
            "pronunciation": "pongka",
            "example": "நல்லா போங்க (nallaa pongka) - Go well (goodbye)",
            "difficulty": "basic",
            "part_of_speech": "verb",
            "word_audio_url": f"{base_url}/vocab_15_word.mp3",
            "example_audio_url": f"{base_url}/vocab_15_example.mp3"
        },
        {
            "word": "ஐயா",
            "translation": "Sir",
            "pronunciation": "aiyyaa",
            "example": "வணக்கம் ஐயா (vanakkam aiyyaa) - Hello sir",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_16_word.mp3",
            "example_audio_url": f"{base_url}/vocab_16_example.mp3"
        },
        {
            "word": "அம்மா",
            "translation": "Madam/Mother",
            "pronunciation": "amma",
            "example": "வணக்கம் அம்மா (vanakkam amma) - Hello madam",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_17_word.mp3",
            "example_audio_url": f"{base_url}/vocab_17_example.mp3"
        },
        {
            "word": "நண்பர்",
            "translation": "Friend",
            "pronunciation": "nanbar",
            "example": "என் நண்பர் ராஜ் (en nanbar raaj) - My friend Raj",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_18_word.mp3",
            "example_audio_url": f"{base_url}/vocab_18_example.mp3"
        },
        {
            "word": "குடும்பம்",
            "translation": "Family",
            "pronunciation": "kudumbam",
            "example": "என் குடும்பம் சென்னையில் இருக்கிறது (en kudumbam chennaiyil irukkiRadhu) - My family is in Chennai",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_19_word.mp3",
            "example_audio_url": f"{base_url}/vocab_19_example.mp3"
        },
        {
            "word": "வேலை",
            "translation": "Work/Job",
            "pronunciation": "velai",
            "example": "என் வேலை பள்ளியில் (en velai palliyil) - My work is at school",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_20_word.mp3",
            "example_audio_url": f"{base_url}/vocab_20_example.mp3"
        },
        {
            "word": "வீடு",
            "translation": "House/Home",
            "pronunciation": "veedu",
            "example": "என் வீடு இங்கே அருகில் (en veedu ingE arugil) - My house is nearby",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_21_word.mp3",
            "example_audio_url": f"{base_url}/vocab_21_example.mp3"
        },
        {
            "word": "ஊர்",
            "translation": "Hometown/Village",
            "pronunciation": "oor",
            "example": "என் ஊர் மதுரை (en oor madhurai) - My hometown is Madurai",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_22_word.mp3",
            "example_audio_url": f"{base_url}/vocab_22_example.mp3"
        },
        {
            "word": "மாணவன்",
            "translation": "Student (male)",
            "pronunciation": "maanavan",
            "example": "நான் கல்லூரி மாணவன் (naan kalloori maanavan) - I am a college student",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_23_word.mp3",
            "example_audio_url": f"{base_url}/vocab_23_example.mp3"
        },
        {
            "word": "மாணவி",
            "translation": "Student (female)",
            "pronunciation": "maanavi",
            "example": "அவள் புதிய மாணவி (aval pudhiya maanavi) - She is a new student",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_24_word.mp3",
            "example_audio_url": f"{base_url}/vocab_24_example.mp3"
        },
        {
            "word": "ஆசிரியர்",
            "translation": "Teacher",
            "pronunciation": "aasiriyar",
            "example": "என் ஆசிரியர் மிகவும் நல்லவர் (en aasiriyar mikavum nallavar) - My teacher is very good",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_25_word.mp3",
            "example_audio_url": f"{base_url}/vocab_25_example.mp3"
        }
    ]
    
    # ✅ CONVERSATIONS - 15 unique greeting and introduction scenarios
    conversations = [
        {
            "title": "First Meeting",
            "scenario": "Meeting someone for the first time",
            "exchanges": [
                {
                    "text": "வணக்கம்! என் பெயர் ராம்",
                    "speaker": "Ram",
                    "translation": "Hello! My name is Ram",
                    "pronunciation": "vanakkam! en peyar raam",
                    "audio_url": f"{base_url}/conv_01_01.mp3"
                },
                {
                    "text": "வணக்கம் ராம்! என் பெயர் சுமதி",
                    "speaker": "Sumathi",
                    "translation": "Hello Ram! My name is Sumathi",
                    "pronunciation": "vanakkam raam! en peyar sumadhi",
                    "audio_url": f"{base_url}/conv_01_02.mp3"
                },
                {
                    "text": "உங்களை சந்தித்ததில் சந்தோஷம்",
                    "speaker": "Ram",
                    "translation": "Happy to meet you",
                    "pronunciation": "ungalai sandhiththadhil sandhosham",
                    "audio_url": f"{base_url}/conv_01_03.mp3"
                }
            ],
            "difficulty": "beginner"
        },
        {
            "title": "Morning Greeting",
            "scenario": "Greeting someone in the morning",
            "exchanges": [
                {
                    "text": "காலை வணக்கம் ஐயா!",
                    "speaker": "Student",
                    "translation": "Good morning sir!",
                    "pronunciation": "kaalai vanakkam aiyyaa!",
                    "audio_url": f"{base_url}/conv_02_01.mp3"
                },
                {
                    "text": "காலை வணக்கம்! எப்படி இருக்கிறீர்கள்?",
                    "speaker": "Teacher",
                    "translation": "Good morning! How are you?",
                    "pronunciation": "kaalai vanakkam! eppaddi irukkiReerkal?",
                    "audio_url": f"{base_url}/conv_02_02.mp3"
                },
                {
                    "text": "நல்லா இருக்கேன், நன்றி",
                    "speaker": "Student",
                    "translation": "I am fine, thank you",
                    "pronunciation": "nallaa irukken, nandri",
                    "audio_url": f"{base_url}/conv_02_03.mp3"
                }
            ],
            "difficulty": "beginner"
        },
        {
            "title": "Asking for Help",
            "scenario": "Politely asking for assistance",
            "exchanges": [
                {
                    "text": "மன்னிக்கவும், தயவு செய்து உதவுங்கள்",
                    "speaker": "Visitor",
                    "translation": "Excuse me, please help me",
                    "pronunciation": "mannikkavum, dhayavu seydhu udhavungal",
                    "audio_url": f"{base_url}/conv_03_01.mp3"
                },
                {
                    "text": "சரி, என்ன உதவி வேண்டும்?",
                    "speaker": "Local",
                    "translation": "Sure, what help do you need?",
                    "pronunciation": "sari, enna udhavi vendum?",
                    "audio_url": f"{base_url}/conv_03_02.mp3"
                },
                {
                    "text": "உங்கள் உதவிக்கு நன்றி",
                    "speaker": "Visitor",
                    "translation": "Thank you for your help",
                    "pronunciation": "ungal udhavikku nandri",
                    "audio_url": f"{base_url}/conv_03_03.mp3"
                }
            ],
            "difficulty": "beginner"
        },
        {
            "title": "Family Introduction",
            "scenario": "Introducing family members",
            "exchanges": [
                {
                    "text": "இது என் குடும்பம்",
                    "speaker": "Host",
                    "translation": "This is my family",
                    "pronunciation": "idhu en kudumbam",
                    "audio_url": f"{base_url}/conv_04_01.mp3"
                },
                {
                    "text": "வணக்கம்! உங்கள் குடும்பம் அழகாக இருக்கிறது",
                    "speaker": "Guest",
                    "translation": "Hello! Your family is beautiful",
                    "pronunciation": "vanakkam! ungal kudumbam azhaagaaga irukkiRadhu",
                    "audio_url": f"{base_url}/conv_04_02.mp3"
                }
            ],
            "difficulty": "beginner"
        },
        {
            "title": "Work Introduction",
            "scenario": "Talking about work",
            "exchanges": [
                {
                    "text": "நீங்கள் என்ன வேலை செய்கிறீர்கள்?",
                    "speaker": "Person A",
                    "translation": "What work do you do?",
                    "pronunciation": "neengal enna velai seykiReerkal?",
                    "audio_url": f"{base_url}/conv_05_01.mp3"
                },
                {
                    "text": "நான் ஆசிரியர். நீங்கள்?",
                    "speaker": "Person B",
                    "translation": "I am a teacher. And you?",
                    "pronunciation": "naan aasiriyar. neengal?",
                    "audio_url": f"{base_url}/conv_05_02.mp3"
                },
                {
                    "text": "நான் மாணவன்",
                    "speaker": "Person A",
                    "translation": "I am a student",
                    "pronunciation": "naan maanavan",
                    "audio_url": f"{base_url}/conv_05_03.mp3"
                }
            ],
            "difficulty": "beginner"
        },
        {
            "title": "Hometown Discussion",
            "scenario": "Asking about hometown",
            "exchanges": [
                {
                    "text": "நீங்கள் எந்த ஊர்?",
                    "speaker": "Person A",
                    "translation": "Which town are you from?",
                    "pronunciation": "neengal endha oor?",
                    "audio_url": f"{base_url}/conv_06_01.mp3"
                },
                {
                    "text": "நான் சென்னை. நீங்கள்?",
                    "speaker": "Person B",
                    "translation": "I am from Chennai. And you?",
                    "pronunciation": "naan chennai. neengal?",
                    "audio_url": f"{base_url}/conv_06_02.mp3"
                }
            ],
            "difficulty": "beginner"
        },
        {
            "title": "Evening Greeting",
            "scenario": "Meeting in the evening",
            "exchanges": [
                {
                    "text": "மாலை வணக்கம்!",
                    "speaker": "Person A",
                    "translation": "Good evening!",
                    "pronunciation": "maalai vanakkam!",
                    "audio_url": f"{base_url}/conv_07_01.mp3"
                },
                {
                    "text": "மாலை வணக்கம்! எப்படி இருக்கிறீர்கள்?",
                    "speaker": "Person B",
                    "translation": "Good evening! How are you?",
                    "pronunciation": "maalai vanakkam! eppaddi irukkiReerkal?",
                    "audio_url": f"{base_url}/conv_07_02.mp3"
                }
            ],
            "difficulty": "beginner"
        },
        {
            "title": "Friend Introduction",
            "scenario": "Introducing a friend",
            "exchanges": [
                {
                    "text": "இது என் நண்பர் ராஜ்",
                    "speaker": "Host",
                    "translation": "This is my friend Raj",
                    "pronunciation": "idhu en nanbar raaj",
                    "audio_url": f"{base_url}/conv_08_01.mp3"
                },
                {
                    "text": "வணக்கம் ராஜ்! என் பெயர் கவிதா",
                    "speaker": "Kavitha",
                    "translation": "Hello Raj! My name is Kavitha",
                    "pronunciation": "vanakkam raaj! en peyar kavithaa",
                    "audio_url": f"{base_url}/conv_08_02.mp3"
                }
            ],
            "difficulty": "beginner"
        },
        {
            "title": "Polite Departure",
            "scenario": "Saying goodbye politely",
            "exchanges": [
                {
                    "text": "நான் போகிறேன். வணக்கம்!",
                    "speaker": "Person A",
                    "translation": "I am going. Goodbye!",
                    "pronunciation": "naan pokiRen. vanakkam!",
                    "audio_url": f"{base_url}/conv_09_01.mp3"
                },
                {
                    "text": "சரி, நல்லா போங்க!",
                    "speaker": "Person B",
                    "translation": "Okay, go well!",
                    "pronunciation": "sari, nallaa pongka!",
                    "audio_url": f"{base_url}/conv_09_02.mp3"
                }
            ],
            "difficulty": "beginner"
        }
    ]

    return {
        "vocabulary": vocabulary,
        "conversations": conversations,
        "lesson_id": lesson_id,
        "lesson_slug": lesson_slug,
        "base_url": base_url
    }

if __name__ == "__main__":
    print("🎯 Creating PERFECT Basic Greetings and Introductions lesson...")
    lesson_data = create_perfect_greetings_lesson()
    print(f"✅ Vocabulary: {len(lesson_data['vocabulary'])} items")
    print("🎯 Ready to add conversations, grammar, exercises with pronunciations...")
