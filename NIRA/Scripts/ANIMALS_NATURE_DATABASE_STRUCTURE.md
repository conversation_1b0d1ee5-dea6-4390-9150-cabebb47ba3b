# Animals & Nature Lesson - Complete Database Structure

## 📊 **EXACT STRUCTURE ANALYSIS**

### **Lesson Metadata:**
- **ID**: `b966c742-d36d-4d94-9e35-7c17a5039487`
- **Path ID**: `6b427613-420f-4586-bce8-2773d722f0b4` (Tamil A1 Course)
- **Title**: `Animals and Nature`
- **Sequence Order**: `24`
- **Lesson Type**: `vocabulary`
- **Difficulty Level**: `1`
- **Has Audio**: `true`
- **Audio Metadata**: `{"generation_date": "2025-06-01 14:19:37", "generated_audio_count": 203}`

### **Content Structure (content_metadata):**

#### **1. VOCABULARY (25 items)**
```json
{
  "word": "நாய்",
  "example": "நாய் குரைக்கிறது (naai k<PERSON>hu) - The dog is barking",
  "difficulty": "basic",
  "translation": "Dog",
  "pronunciation": "naai",
  "part_of_speech": "noun",
  "word_audio_url": "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/animals_nature/vocab_01_word.mp3",
  "example_audio_url": "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/animals_nature/vocab_01_example.mp3"
}
```

**Required Fields for Each Vocabulary Item:**
- `word` - Tamil word
- `translation` - English translation
- `pronunciation` - Romanized pronunciation
- `example` - Tamil example sentence with English translation
- `difficulty` - "basic" for A1 level
- `part_of_speech` - "noun", "verb", "adjective", etc.
- `word_audio_url` - Audio URL for the word
- `example_audio_url` - Audio URL for the example

#### **2. CONVERSATIONS (15 conversations)**
```json
{
  "title": "At the Zoo",
  "scenario": "Visiting animals at the zoo",
  "difficulty": "beginner",
  "exchanges": [
    {
      "text": "இது என்ன விலங்கு?",
      "speaker": "Child",
      "translation": "What animal is this?",
      "pronunciation": "idhu enna vilangku?",
      "audio_url": "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/animals_nature/conv_01_01.mp3"
    },
    {
      "text": "இது யானை",
      "speaker": "Parent", 
      "translation": "This is an elephant",
      "pronunciation": "idhu yaanai",
      "audio_url": "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/animals_nature/conv_01_02.mp3"
    }
  ]
}
```

**Required Fields for Each Conversation:**
- `title` - Conversation title
- `scenario` - Context description
- `difficulty` - "beginner" for A1
- `exchanges` - Array of conversation exchanges
  - `text` - Tamil text
  - `speaker` - Speaker identifier
  - `translation` - English translation
  - `pronunciation` - Romanized pronunciation
  - `audio_url` - Audio URL for the exchange

#### **3. GRAMMAR POINTS (10 points)**
```json
{
  "rule": "Present Continuous Tense",
  "explanation": "Use கிறது/கிறார்/கிறேன் to show ongoing actions in Tamil",
  "examples": [
    "நாய் குரைக்கிறது (naai kuraikkirathu) - The dog is barking",
    "பறவை பறக்கிறது (paravai parakkiRathu) - The bird is flying",
    "மீன் நீந்துகிறது (meen neenthukiRathu) - The fish is swimming"
  ],
  "tips": "Practice with animal actions to remember the pattern",
  "examples_audio_urls": [
    "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/animals_nature/grammar_01_01.mp3",
    "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/animals_nature/grammar_01_02.mp3",
    "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/animals_nature/grammar_01_03.mp3"
  ]
}
```

**Required Fields for Each Grammar Point:**
- `rule` - Grammar rule name
- `explanation` - Clear explanation in English
- `examples` - Array of 3 Tamil examples with English translations
- `tips` - Learning tip
- `examples_audio_urls` - Array of 3 audio URLs for examples

#### **4. EXERCISES (24 exercises)**
```json
{
  "type": "multiple_choice",
  "points": 10,
  "question": "What is the Tamil word for dog?",
  "options": ["நாய்", "பூனை", "யானை", "சிங்கம்"],
  "correctAnswer": 0,
  "explanation": "நாய் (naai) means dog in Tamil",
  "options_audio_urls": [
    "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/animals_nature/exercise_01_option_01.mp3",
    "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/animals_nature/exercise_01_option_02.mp3",
    "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/animals_nature/exercise_01_option_03.mp3",
    "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/animals_nature/exercise_01_option_04.mp3"
  ]
}
```

**Required Fields for Each Exercise:**
- `type` - "multiple_choice", "fill_in_blank", "matching", "translation"
- `points` - Point value (10, 15, or 20)
- `question` - Question text in English
- `options` - Array of 4 options (Tamil or English)
- `correctAnswer` - Index of correct answer (0-3)
- `explanation` - Explanation of correct answer
- `options_audio_urls` - Array of 4 audio URLs for options

### **Audio URL Pattern:**
Base URL: `https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/animals_nature/`

**Vocabulary Audio:**
- Word: `vocab_{01-25}_word.mp3`
- Example: `vocab_{01-25}_example.mp3`

**Conversation Audio:**
- Exchange: `conv_{01-15}_{01-02}.mp3`

**Grammar Audio:**
- Examples: `grammar_{01-10}_{01-03}.mp3`

**Exercise Audio:**
- Options: `exercise_{01-24}_option_{01-04}.mp3`

### **Total Audio Files: 203**
- Vocabulary: 50 files (25 words + 25 examples)
- Conversations: 30 files (15 conversations × 2 exchanges)
- Grammar: 30 files (10 points × 3 examples)
- Exercises: 96 files (24 exercises × 4 options)

## 🎯 **REPLICATION REQUIREMENTS**

To replicate this structure for other lessons:

1. **Same content counts**: 25 vocab, 15 conversations, 10 grammar, 24 exercises
2. **Same field structure**: All required fields must be present
3. **Same audio pattern**: Follow exact URL naming convention
4. **Same lesson metadata**: Update title, sequence_order, but keep structure
5. **Same path_id**: Use `6b427613-420f-4586-bce8-2773d722f0b4` for Tamil A1

This structure is proven to work 100% in the iOS app with complete audio integration.
