#!/usr/bin/env python3
"""
Complete Body Parts and Health Vocabulary
Add full 25 vocabulary items following quality standards
"""

import requests
import json

def create_complete_vocabulary():
    """Create complete 25-item vocabulary for body parts and health"""
    lesson_slug = "body_parts_and_health"
    base_url = f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/{lesson_slug}"
    
    vocabulary = [
        {
            "word": "தலை",
            "translation": "Head",
            "pronunciation": "thalai",
            "example": "என் தலை வலிக்கிறது (en thalai valikkiradhu) - My head is hurting",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_01_word.mp3",
            "example_audio_url": f"{base_url}/vocab_01_example.mp3"
        },
        {
            "word": "கண்",
            "translation": "Eye",
            "pronunciation": "kan",
            "example": "என் கண் சிவந்திருக்கிறது (en kan sivandhurukkiRadhu) - My eye is red",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_02_word.mp3",
            "example_audio_url": f"{base_url}/vocab_02_example.mp3"
        },
        {
            "word": "காது",
            "translation": "Ear",
            "pronunciation": "kaadhu",
            "example": "என் காது வலிக்கிறது (en kaadhu valikkiradhu) - My ear is hurting",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_03_word.mp3",
            "example_audio_url": f"{base_url}/vocab_03_example.mp3"
        },
        {
            "word": "மூக்கு",
            "translation": "Nose",
            "pronunciation": "mookku",
            "example": "என் மூக்கில் சளி இருக்கிறது (en mookkil sali irukkiRadhu) - I have a runny nose",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_04_word.mp3",
            "example_audio_url": f"{base_url}/vocab_04_example.mp3"
        },
        {
            "word": "வாய்",
            "translation": "Mouth",
            "pronunciation": "vaay",
            "example": "வாய் திறந்து பாருங்கள் (vaay thiRandhu paarungal) - Open your mouth and see",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_05_word.mp3",
            "example_audio_url": f"{base_url}/vocab_05_example.mp3"
        },
        {
            "word": "பல்",
            "translation": "Tooth",
            "pronunciation": "pal",
            "example": "என் பல் வலிக்கிறது (en pal valikkiradhu) - My tooth is hurting",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_06_word.mp3",
            "example_audio_url": f"{base_url}/vocab_06_example.mp3"
        },
        {
            "word": "நாக்கு",
            "translation": "Tongue",
            "pronunciation": "naakku",
            "example": "நாக்கு காட்டுங்கள் (naakku kaattungal) - Show your tongue",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_07_word.mp3",
            "example_audio_url": f"{base_url}/vocab_07_example.mp3"
        },
        {
            "word": "கழுத்து",
            "translation": "Neck",
            "pronunciation": "kazhutthu",
            "example": "கழுத்து வலிக்கிறது (kazhutthu valikkiradhu) - Neck is hurting",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_08_word.mp3",
            "example_audio_url": f"{base_url}/vocab_08_example.mp3"
        },
        {
            "word": "தோள்",
            "translation": "Shoulder",
            "pronunciation": "thol",
            "example": "என் தோள் வலிக்கிறது (en thol valikkiradhu) - My shoulder is hurting",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_09_word.mp3",
            "example_audio_url": f"{base_url}/vocab_09_example.mp3"
        },
        {
            "word": "கை",
            "translation": "Hand",
            "pronunciation": "kai",
            "example": "கை கழுவுங்கள் (kai kazhuvungal) - Wash your hands",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_10_word.mp3",
            "example_audio_url": f"{base_url}/vocab_10_example.mp3"
        },
        {
            "word": "விரல்",
            "translation": "Finger",
            "pronunciation": "viral",
            "example": "என் விரல் வலிக்கிறது (en viral valikkiradhu) - My finger is hurting",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_11_word.mp3",
            "example_audio_url": f"{base_url}/vocab_11_example.mp3"
        },
        {
            "word": "மார்பு",
            "translation": "Chest",
            "pronunciation": "maarbu",
            "example": "மார்பு வலிக்கிறது (maarbu valikkiradhu) - Chest is hurting",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_12_word.mp3",
            "example_audio_url": f"{base_url}/vocab_12_example.mp3"
        },
        {
            "word": "வயிறு",
            "translation": "Stomach",
            "pronunciation": "vayiRu",
            "example": "என் வயிறு வலிக்கிறது (en vayiRu valikkiradhu) - My stomach is hurting",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_13_word.mp3",
            "example_audio_url": f"{base_url}/vocab_13_example.mp3"
        },
        {
            "word": "முதுகு",
            "translation": "Back",
            "pronunciation": "mudhugu",
            "example": "என் முதுகு வலிக்கிறது (en mudhugu valikkiradhu) - My back is hurting",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_14_word.mp3",
            "example_audio_url": f"{base_url}/vocab_14_example.mp3"
        },
        {
            "word": "கால்",
            "translation": "Leg/Foot",
            "pronunciation": "kaal",
            "example": "என் கால் வலிக்கிறது (en kaal valikkiradhu) - My leg is hurting",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_15_word.mp3",
            "example_audio_url": f"{base_url}/vocab_15_example.mp3"
        },
        {
            "word": "முழங்கால்",
            "translation": "Knee",
            "pronunciation": "muzhangkaal",
            "example": "முழங்கால் வலிக்கிறது (muzhangkaal valikkiradhu) - Knee is hurting",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_16_word.mp3",
            "example_audio_url": f"{base_url}/vocab_16_example.mp3"
        },
        {
            "word": "உடல்நிலை",
            "translation": "Health condition",
            "pronunciation": "udalnilai",
            "example": "உங்கள் உடல்நிலை எப்படி? (ungal udalnilai eppaddi?) - How is your health?",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_17_word.mp3",
            "example_audio_url": f"{base_url}/vocab_17_example.mp3"
        },
        {
            "word": "வலி",
            "translation": "Pain",
            "pronunciation": "vali",
            "example": "எங்கே வலி இருக்கிறது? (engE vali irukkiRadhu?) - Where is the pain?",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_18_word.mp3",
            "example_audio_url": f"{base_url}/vocab_18_example.mp3"
        },
        {
            "word": "காய்ச்சல்",
            "translation": "Fever",
            "pronunciation": "kaaychhal",
            "example": "எனக்கு காய்ச்சல் இருக்கிறது (enakku kaaychhal irukkiRadhu) - I have fever",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_19_word.mp3",
            "example_audio_url": f"{base_url}/vocab_19_example.mp3"
        },
        {
            "word": "தலைவலி",
            "translation": "Headache",
            "pronunciation": "thalai vali",
            "example": "எனக்கு தலைவலி இருக்கிறது (enakku thalai vali irukkiRadhu) - I have a headache",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_20_word.mp3",
            "example_audio_url": f"{base_url}/vocab_20_example.mp3"
        },
        {
            "word": "இருமல்",
            "translation": "Cough",
            "pronunciation": "irumal",
            "example": "எனக்கு இருமல் இருக்கிறது (enakku irumal irukkiRadhu) - I have a cough",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_21_word.mp3",
            "example_audio_url": f"{base_url}/vocab_21_example.mp3"
        },
        {
            "word": "சளி",
            "translation": "Cold/Runny nose",
            "pronunciation": "sali",
            "example": "எனக்கு சளி இருக்கிறது (enakku sali irukkiRadhu) - I have a cold",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_22_word.mp3",
            "example_audio_url": f"{base_url}/vocab_22_example.mp3"
        },
        {
            "word": "மருந்து",
            "translation": "Medicine",
            "pronunciation": "marundhu",
            "example": "மருந்து சாப்பிடுங்கள் (marundhu saappidungal) - Take medicine",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_23_word.mp3",
            "example_audio_url": f"{base_url}/vocab_23_example.mp3"
        },
        {
            "word": "மருத்துவர்",
            "translation": "Doctor",
            "pronunciation": "maruththuvar",
            "example": "மருத்துவரை பார்க்க வேண்டும் (maruththuvarai paarkka vendum) - Need to see a doctor",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_24_word.mp3",
            "example_audio_url": f"{base_url}/vocab_24_example.mp3"
        },
        {
            "word": "ஆரோக்கியம்",
            "translation": "Health/Wellness",
            "pronunciation": "aarokkiyam",
            "example": "ஆரோக்கியம் முக்கியம் (aarokkiyam mukkiyam) - Health is important",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_25_word.mp3",
            "example_audio_url": f"{base_url}/vocab_25_example.mp3"
        }
    ]
    
    return vocabulary

def update_vocabulary():
    """Update the lesson with complete vocabulary"""
    # Get current lesson
    url = "https://lyaojebttnqilmdosmjk.supabase.co/rest/v1/lessons?title=eq.Body%20Parts%20and%20Health&select=id,content_metadata"
    headers = {
        "apikey": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c",
        "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"
    }
    
    response = requests.get(url, headers=headers)
    if response.status_code != 200:
        return False
    
    data = response.json()
    if not data:
        return False
    
    lesson_data = data[0]
    lesson_id = lesson_data['id']
    content_metadata = lesson_data['content_metadata']
    
    # Update vocabulary
    content_metadata['vocabulary'] = create_complete_vocabulary()
    
    # Update lesson in database
    update_url = f"https://lyaojebttnqilmdosmjk.supabase.co/rest/v1/lessons?id=eq.{lesson_id}"
    update_headers = {
        **headers,
        "Content-Type": "application/json",
        "Prefer": "return=minimal"
    }
    
    update_data = {"content_metadata": content_metadata}
    response = requests.patch(update_url, headers=update_headers, json=update_data)
    
    return response.status_code == 204

def main():
    """Main function"""
    print("🎯 Updating Body Parts and Health vocabulary to 25 items...")
    
    success = update_vocabulary()
    
    if success:
        print("✅ SUCCESS: Vocabulary updated to 25 items!")
        print("📊 Complete vocabulary with:")
        print("   - 16 body parts (head to knee)")
        print("   - 9 health terms (conditions, symptoms, treatment)")
        print("   - All with Tamil examples and pronunciations")
        print("   - Culturally appropriate and A1-level appropriate")
    else:
        print("❌ FAILED: Could not update vocabulary")

if __name__ == "__main__":
    main()
