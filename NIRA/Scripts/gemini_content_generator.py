#!/usr/bin/env python3
"""
Gemini Flash 2.0 Content Generator
Generates complete A1 Tamil lesson content
"""

import requests
import json
import google.generativeai as genai
from typing import Dict, Any

class GeminiContentGenerator:
    def __init__(self, api_key: str):
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel('gemini-2.0-flash-exp')
        
    def generate_lesson_content(self, lesson_title: str) -> Dict[str, Any]:
        """Generate complete lesson content using Gemini Flash 2.0"""
        
        prompt = f"""
Generate a complete A1 Tamil language lesson for "{lesson_title}".

REQUIREMENTS:
- 25 unique vocabulary items
- 15 unique conversation scenarios  
- 10 unique grammar points
- 12 unique exercises

VOCABULARY FORMAT:
Each item needs: Tamil word, English translation, romanized pronunciation, example sentence with translation

CONVERSATIONS FORMAT:
Each scenario needs: title, 2-3 exchanges with speaker, Tamil text, English translation, romanized pronunciation

GRAMMAR FORMAT:
Each point needs: rule explanation, 3 examples with pronunciations, cultural tips

EXERCISES FORMAT:
Multiple choice questions with 4 options, pronunciations for all options, correct answer index, explanation

QUALITY REQUIREMENTS:
- All content must be unique (no repetitions)
- Culturally authentic Tamil context
- A1 beginner level appropriate
- Proper Tamil script with accurate romanization
- Real-world practical scenarios
- No placeholder content

OUTPUT FORMAT:
Return valid JSON with this exact structure:
{{
  "vocabulary": [
    {{
      "word": "Tamil word",
      "translation": "English translation", 
      "pronunciation": "romanized pronunciation",
      "example": "Tamil example (pronunciation) - English translation",
      "difficulty": "basic",
      "part_of_speech": "noun/verb/adjective",
      "cultural_note": "optional cultural context"
    }}
  ],
  "conversations": [
    {{
      "title": "Scenario title",
      "scenario": "Brief description",
      "exchanges": [
        {{
          "text": "Tamil text",
          "speaker": "Speaker name",
          "translation": "English translation",
          "pronunciation": "romanized pronunciation"
        }}
      ],
      "difficulty": "beginner"
    }}
  ],
  "grammar_points": [
    {{
      "rule": "Grammar rule title",
      "explanation": "Clear explanation",
      "examples": [
        "Tamil example 1 (pronunciation) - English translation",
        "Tamil example 2 (pronunciation) - English translation", 
        "Tamil example 3 (pronunciation) - English translation"
      ],
      "tips": "Cultural or usage tips"
    }}
  ],
  "exercises": [
    {{
      "type": "multiple_choice",
      "points": 10,
      "question": "Question text",
      "options": ["Option 1", "Option 2", "Option 3", "Option 4"],
      "options_pronunciations": ["pronunciation1", "pronunciation2", "pronunciation3", "pronunciation4"],
      "correctAnswer": 0,
      "explanation": "Why this answer is correct"
    }}
  ]
}}

Generate content specifically for: {lesson_title}
"""

        try:
            response = self.model.generate_content(prompt)
            
            # Parse JSON response
            content_text = response.text
            
            # Clean up response (remove markdown formatting if present)
            if "```json" in content_text:
                content_text = content_text.split("```json")[1].split("```")[0]
            elif "```" in content_text:
                content_text = content_text.split("```")[1].split("```")[0]
            
            content = json.loads(content_text)
            
            # Add audio URLs
            content = self._add_audio_urls(content, lesson_title)
            
            return content
            
        except Exception as e:
            print(f"❌ Gemini generation failed: {e}")
            return self._fallback_content(lesson_title)
    
    def _add_audio_urls(self, content: Dict[str, Any], lesson_title: str) -> Dict[str, Any]:
        """Add audio URL structure to generated content"""
        lesson_slug = lesson_title.lower().replace(' ', '_').replace('&', 'and')
        base_url = f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/{lesson_slug}"
        
        # Add vocabulary audio URLs
        for i, vocab in enumerate(content.get("vocabulary", [])):
            vocab["word_audio_url"] = f"{base_url}/vocab_{i+1:02d}_word.mp3"
            vocab["example_audio_url"] = f"{base_url}/vocab_{i+1:02d}_example.mp3"
        
        # Add conversation audio URLs
        for i, conv in enumerate(content.get("conversations", [])):
            for j, exchange in enumerate(conv.get("exchanges", [])):
                exchange["audio_url"] = f"{base_url}/conv_{i+1:02d}_{j+1:02d}.mp3"
        
        # Add grammar audio URLs
        for i, grammar in enumerate(content.get("grammar_points", [])):
            grammar["examples_audio_urls"] = [
                f"{base_url}/grammar_{i+1:02d}_01.mp3",
                f"{base_url}/grammar_{i+1:02d}_02.mp3",
                f"{base_url}/grammar_{i+1:02d}_03.mp3"
            ]
        
        # Add exercise audio URLs
        for i, exercise in enumerate(content.get("exercises", [])):
            exercise["options_audio_urls"] = [
                f"{base_url}/exercise_{i+1:02d}_option_01.mp3",
                f"{base_url}/exercise_{i+1:02d}_option_02.mp3",
                f"{base_url}/exercise_{i+1:02d}_option_03.mp3",
                f"{base_url}/exercise_{i+1:02d}_option_04.mp3"
            ]
        
        return content
    
    def _fallback_content(self, lesson_title: str) -> Dict[str, Any]:
        """Fallback content if Gemini fails"""
        print(f"⚠️ Using fallback content for {lesson_title}")
        
        return {
            "vocabulary": [],
            "conversations": [],
            "grammar_points": [],
            "exercises": []
        }

# Lesson-specific content generators for better quality
class LessonSpecificGenerator:
    def __init__(self, gemini_generator: GeminiContentGenerator):
        self.gemini = gemini_generator
    
    def generate_clothing_shopping(self) -> Dict[str, Any]:
        """Generate Clothing and Shopping lesson"""
        return self.gemini.generate_lesson_content("Clothing and Shopping")
    
    def generate_colors_description(self) -> Dict[str, Any]:
        """Generate Colors and Description lesson"""
        return self.gemini.generate_lesson_content("Colors and Description")
    
    def generate_common_verbs(self) -> Dict[str, Any]:
        """Generate Common Verbs and Actions lesson"""
        return self.gemini.generate_lesson_content("Common Verbs and Actions")
    
    def generate_daily_routines(self) -> Dict[str, Any]:
        """Generate Daily Routines and Activities lesson"""
        return self.gemini.generate_lesson_content("Daily Routines and Activities")

if __name__ == "__main__":
    # Test the generator
    api_key = "AIzaSyBVZ_5-2Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8"  # Replace with actual key
    generator = GeminiContentGenerator(api_key)
    
    # Test with a lesson
    content = generator.generate_lesson_content("Clothing and Shopping")
    print(f"Generated content with {len(content.get('vocabulary', []))} vocabulary items")
    print(f"Generated {len(content.get('conversations', []))} conversations")
    print(f"Generated {len(content.get('exercises', []))} exercises")
