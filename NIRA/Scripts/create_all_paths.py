#!/usr/bin/env python3
"""
Create All Learning Paths for NIRA
Creates A2, B1, B2, C1, C2 learning paths for Tamil

This script creates the learning path structure needed for all levels.
"""

import json
import requests
from typing import Dict, List, Any

# Supabase configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

class PathCreator:
    """Creates learning paths for all levels"""
    
    def __init__(self):
        self.headers = {
            'apikey': SUPABASE_KEY,
            'Authorization': f'Bearer {SUPABASE_KEY}',
            'Content-Type': 'application/json'
        }
        
        # Tamil language and agent IDs
        self.tamil_language_id = "4df28de4-168f-4d8e-a304-b785e9295644"
        self.agent_id = "7f458eb9-5b56-4e91-97ea-002ddc3d05b3"
    
    def create_learning_path(self, level: str, name: str, description: str, estimated_hours: int) -> str:
        """Create a learning path and return its ID"""
        print(f"🔄 Creating {level} learning path...")
        
        path_data = {
            'name': name,
            'description': description,
            'language_id': self.tamil_language_id,
            'agent_id': self.agent_id,
            'level': level,
            'estimated_hours': estimated_hours,
            'is_active': True
        }
        
        try:
            response = requests.post(f"{SUPABASE_URL}/rest/v1/learning_paths", json=path_data, headers=self.headers)
            
            if response.status_code == 201:
                path_id = response.json()[0]['id']
                print(f"✅ Created {level} path: {path_id}")
                return path_id
            else:
                print(f"❌ Failed to create {level} path: {response.status_code}")
                print(f"Response: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Error creating {level} path: {e}")
            return None
    
    def create_all_paths(self) -> Dict[str, str]:
        """Create all learning paths"""
        print("🚀 CREATING ALL TAMIL LEARNING PATHS")
        print("=" * 50)
        
        paths = {}
        
        # A2 Elementary
        path_id = self.create_learning_path(
            "A2", 
            "Tamil A2 - Elementary",
            "Elementary level Tamil with complex conversations and advanced grammar",
            80
        )
        if path_id:
            paths['A2'] = path_id
        
        # B1 Intermediate  
        path_id = self.create_learning_path(
            "B1",
            "Tamil B1 - Intermediate", 
            "Intermediate level Tamil with complex vocabulary and sophisticated grammar",
            100
        )
        if path_id:
            paths['B1'] = path_id
        
        # B2 Upper-Intermediate
        path_id = self.create_learning_path(
            "B2",
            "Tamil B2 - Upper-Intermediate",
            "Upper-intermediate Tamil with specialized vocabulary and advanced concepts", 
            120
        )
        if path_id:
            paths['B2'] = path_id
        
        # C1 Advanced
        path_id = self.create_learning_path(
            "C1",
            "Tamil C1 - Advanced",
            "Advanced Tamil with sophisticated vocabulary and complex theoretical concepts",
            150
        )
        if path_id:
            paths['C1'] = path_id
        
        # C2 Mastery
        path_id = self.create_learning_path(
            "C2", 
            "Tamil C2 - Mastery",
            "Mastery level Tamil with native-level vocabulary and classical literary concepts",
            200
        )
        if path_id:
            paths['C2'] = path_id
        
        return paths
    
    def save_path_ids(self, paths: Dict[str, str]) -> None:
        """Save path IDs to files for validators"""
        for level, path_id in paths.items():
            with open(f'/tmp/{level.lower()}_path_id.txt', 'w') as f:
                f.write(path_id)
            print(f"💾 Saved {level} path ID: {path_id}")

def main():
    """Main function"""
    print("🎯 TAMIL LEARNING PATH CREATOR")
    print("Creating paths for A2, B1, B2, C1, C2 levels")
    print("=" * 50)
    
    creator = PathCreator()
    
    # Create all paths
    paths = creator.create_all_paths()
    
    # Display results
    print(f"\n📊 PATH CREATION RESULTS:")
    print(f"  • Total Paths Created: {len(paths)}")
    
    for level, path_id in paths.items():
        print(f"  • {level}: {path_id}")
    
    # Save path IDs
    if paths:
        creator.save_path_ids(paths)
        
        print(f"\n✅ SUCCESS! Created {len(paths)} learning paths")
        print(f"\n📋 NEXT STEPS:")
        print("1. ✅ Learning paths created")
        print("2. 🔄 Run level-specific vocabulary validators")
        print("3. 🔄 Run conversation, grammar, exercises validators")
        print("4. 🎵 Generate audio for all levels")
    else:
        print(f"\n❌ No paths were created successfully")

if __name__ == "__main__":
    main()
