#!/usr/bin/env python3
"""
PROPER Lesson Generator - Following Quality Checklist
Uses Gemini Flash 2.0 + Claude + GPT-4 validation as specified
"""

import os
import json
import requests
import time
import google.generativeai as genai
import anthropic
import openai
from typing import Dict, Any, List

class ProperLessonGenerator:
    def __init__(self):
        # API Keys - Using actual keys from environment or hardcoded
        self.gemini_key = os.getenv('GEMINI_API_KEY', 'AIzaSyBVZ_5-2Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8')
        self.claude_key = os.getenv('CLAUDE_API_KEY', 'sk-ant-api03-...')
        self.openai_key = os.getenv('OPENAI_API_KEY', 'sk-proj-...')
        
        # Initialize AI clients
        genai.configure(api_key=self.gemini_key)
        self.gemini_model = genai.GenerativeModel('gemini-2.0-flash-exp')
        self.claude_client = anthropic.Anthropic(api_key=self.claude_key)
        self.openai_client = openai.OpenAI(api_key=self.openai_key)
        
        # Supabase config
        self.supabase_url = "https://lyaojebttnqilmdosmjk.supabase.co"
        self.supabase_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

    def generate_with_gemini(self, lesson_title: str) -> Dict[str, Any]:
        """Generate authentic Tamil lesson content using Gemini Flash 2.0"""
        
        prompt = f"""
Create a complete A1 Tamil language lesson for "{lesson_title}".

CRITICAL REQUIREMENTS:
- ALL content must be authentic Tamil with proper script
- ALL vocabulary must be real Tamil words related to {lesson_title}
- ALL conversations must be realistic scenarios about {lesson_title}
- ALL grammar must be actual Tamil grammar rules
- NO placeholder content, NO generic templates
- Cultural authenticity is MANDATORY

EXACT STRUCTURE REQUIRED:

VOCABULARY (exactly 25 items):
Each item needs:
- word: Real Tamil word in Tamil script
- translation: English translation
- pronunciation: Accurate romanized pronunciation
- example: Tamil sentence using the word with pronunciation and English translation
- difficulty: "basic"
- part_of_speech: actual part of speech

CONVERSATIONS (exactly 15 scenarios):
Each scenario needs:
- title: Specific scenario name related to {lesson_title}
- scenario: Brief description
- exchanges: 2-3 realistic exchanges between speakers
  - text: Tamil text in Tamil script
  - speaker: Speaker name (Tamil names preferred)
  - translation: English translation
  - pronunciation: Accurate romanized pronunciation

GRAMMAR (exactly 10 points):
Each point needs:
- rule: Specific Tamil grammar rule related to {lesson_title}
- explanation: Clear explanation in English
- examples: 3 Tamil examples with pronunciations and translations
- tips: Cultural or usage tips

EXERCISES (exactly 12 questions):
Each exercise needs:
- type: "multiple_choice"
- points: 10
- question: Question testing lesson content
- options: 4 options (mix of Tamil and English as appropriate)
- options_pronunciations: Romanized pronunciations for all options
- correctAnswer: Index of correct answer (0-3)
- explanation: Why the answer is correct

SPECIFIC FOCUS FOR {lesson_title}:
Generate content that is specifically about {lesson_title}. For example:
- If Colors: actual Tamil color words, conversations about describing things
- If Food: actual Tamil food terms, conversations about cooking/eating
- If Family: actual Tamil family relationship terms, conversations about family

OUTPUT AS VALID JSON:
{{
  "vocabulary": [...],
  "conversations": [...], 
  "grammar_points": [...],
  "exercises": [...]
}}

Generate authentic, culturally appropriate Tamil content for: {lesson_title}
"""

        try:
            print(f"🤖 Generating content with Gemini for {lesson_title}...")
            response = self.gemini_model.generate_content(prompt)
            
            # Clean and parse response
            content_text = response.text.strip()
            
            # Remove markdown formatting
            if "```json" in content_text:
                content_text = content_text.split("```json")[1].split("```")[0]
            elif "```" in content_text:
                content_text = content_text.split("```")[1].split("```")[0]
            
            content = json.loads(content_text)
            
            # Add audio URLs
            content = self._add_audio_urls(content, lesson_title)
            
            print(f"✅ Generated: {len(content.get('vocabulary', []))} vocab, {len(content.get('conversations', []))} conversations, {len(content.get('exercises', []))} exercises")
            return content
            
        except Exception as e:
            print(f"❌ Gemini generation failed: {e}")
            raise Exception(f"Content generation failed: {e}")

    def validate_with_claude(self, content: Dict[str, Any], lesson_title: str) -> Dict[str, Any]:
        """Validate content quality with Claude following quality checklist"""
        
        validation_prompt = f"""
CRITICAL QUALITY VALIDATION for Tamil A1 lesson "{lesson_title}"

CONTENT TO VALIDATE:
{json.dumps(content, indent=2, ensure_ascii=False)}

COMPREHENSIVE QUALITY CHECKLIST - VALIDATE EACH POINT:

1. CONTENT COUNTS (CRITICAL):
   - Exactly 25 unique vocabulary items? 
   - Exactly 15 unique conversation scenarios?
   - Exactly 10 unique grammar points?
   - At least 12 unique exercises?

2. UNIQUENESS CHECK (CRITICAL):
   - All vocabulary words are different?
   - All conversation titles/content are different?
   - All exercise questions are different?
   - No repeated content within lesson?

3. PRONUNCIATION COMPLETENESS (CRITICAL):
   - All exercises have options_pronunciations array?
   - Pronunciation arrays match option arrays in length?
   - No empty or null pronunciations?

4. CULTURAL AUTHENTICITY (CRITICAL):
   - Content reflects authentic Tamil culture?
   - Examples use realistic Tamil scenarios?
   - No cultural stereotypes or inaccuracies?
   - Appropriate for Tamil learners?

5. A1 DIFFICULTY LEVEL (CRITICAL):
   - Vocabulary appropriate for beginners?
   - Grammar concepts are basic level?
   - Sentence structures are simple?
   - No advanced Tamil concepts?

6. TECHNICAL CORRECTNESS (CRITICAL):
   - Proper Tamil script used?
   - Accurate romanization?
   - Complete English translations?
   - No placeholder content like "word1", "example1"?

7. LESSON-SPECIFIC CONTENT (CRITICAL):
   - Content actually relates to {lesson_title}?
   - Vocabulary is topic-specific?
   - Conversations are about the lesson topic?
   - Not generic content that could apply to any lesson?

RETURN VALIDATION RESULT AS JSON:
{{
  "vocabulary_count_valid": boolean,
  "conversations_count_valid": boolean,
  "grammar_count_valid": boolean,
  "exercises_count_valid": boolean,
  "vocabulary_unique": boolean,
  "conversations_unique": boolean,
  "exercises_unique": boolean,
  "pronunciations_complete": boolean,
  "cultural_authentic": boolean,
  "a1_appropriate": boolean,
  "technical_correct": boolean,
  "lesson_specific": boolean,
  "overall_quality_score": number (0-100),
  "critical_issues": ["list of critical issues that MUST be fixed"],
  "recommendations": ["list of improvements"],
  "passes_validation": boolean
}}

BE STRICT - this content will be used by real Tamil learners.
"""

        try:
            print(f"🔍 Claude validation for {lesson_title}...")
            response = self.claude_client.messages.create(
                model="claude-3-5-sonnet-20241022",
                max_tokens=2000,
                messages=[{"role": "user", "content": validation_prompt}]
            )
            
            result_text = response.content[0].text
            
            # Parse JSON response
            if "```json" in result_text:
                result_text = result_text.split("```json")[1].split("```")[0]
            
            validation_result = json.loads(result_text)
            
            score = validation_result.get('overall_quality_score', 0)
            passes = validation_result.get('passes_validation', False)
            
            print(f"📊 Claude score: {score}/100, Passes: {passes}")
            
            if not passes:
                issues = validation_result.get('critical_issues', [])
                print(f"❌ Critical issues: {issues}")
            
            return validation_result
            
        except Exception as e:
            print(f"❌ Claude validation failed: {e}")
            return {"passes_validation": False, "critical_issues": [f"Validation error: {e}"]}

    def validate_with_gpt4(self, content: Dict[str, Any], lesson_title: str) -> Dict[str, Any]:
        """Second validation with GPT-4 Turbo"""
        
        validation_prompt = f"""
As a Tamil language expert, provide a second opinion validation for this A1 lesson "{lesson_title}":

{json.dumps(content, indent=2, ensure_ascii=False)}

Focus on:
1. Tamil language accuracy and authenticity
2. Cultural appropriateness 
3. A1 difficulty level appropriateness
4. Content quality and realism
5. No placeholder or generic content

Return JSON with validation results and specific feedback.
"""

        try:
            print(f"🔍 GPT-4 validation for {lesson_title}...")
            response = self.openai_client.chat.completions.create(
                model="gpt-4-turbo",
                messages=[
                    {"role": "system", "content": "You are a Tamil language expert providing quality validation."},
                    {"role": "user", "content": validation_prompt}
                ],
                max_tokens=1500,
                temperature=0.1
            )
            
            result_text = response.choices[0].message.content
            
            # Parse JSON response
            if "```json" in result_text:
                result_text = result_text.split("```json")[1].split("```")[0]
            
            validation_result = json.loads(result_text)
            
            score = validation_result.get('overall_quality_score', 0)
            passes = validation_result.get('passes_validation', False)
            
            print(f"📊 GPT-4 score: {score}/100, Passes: {passes}")
            
            return validation_result
            
        except Exception as e:
            print(f"❌ GPT-4 validation failed: {e}")
            return {"passes_validation": False, "critical_issues": [f"GPT-4 validation error: {e}"]}

    def _add_audio_urls(self, content: Dict[str, Any], lesson_title: str) -> Dict[str, Any]:
        """Add proper audio URL structure"""
        lesson_slug = lesson_title.lower().replace(' ', '_').replace('&', 'and')
        base_url = f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/{lesson_slug}"
        
        # Add vocabulary audio URLs
        for i, vocab in enumerate(content.get("vocabulary", [])):
            vocab["word_audio_url"] = f"{base_url}/vocab_{i+1:02d}_word.mp3"
            vocab["example_audio_url"] = f"{base_url}/vocab_{i+1:02d}_example.mp3"
        
        # Add conversation audio URLs
        for i, conv in enumerate(content.get("conversations", [])):
            for j, exchange in enumerate(conv.get("exchanges", [])):
                exchange["audio_url"] = f"{base_url}/conv_{i+1:02d}_{j+1:02d}.mp3"
        
        # Add grammar audio URLs
        for i, grammar in enumerate(content.get("grammar_points", [])):
            grammar["examples_audio_urls"] = [
                f"{base_url}/grammar_{i+1:02d}_01.mp3",
                f"{base_url}/grammar_{i+1:02d}_02.mp3",
                f"{base_url}/grammar_{i+1:02d}_03.mp3"
            ]
        
        # Add exercise audio URLs
        for i, exercise in enumerate(content.get("exercises", [])):
            exercise["options_audio_urls"] = [
                f"{base_url}/exercise_{i+1:02d}_option_01.mp3",
                f"{base_url}/exercise_{i+1:02d}_option_02.mp3",
                f"{base_url}/exercise_{i+1:02d}_option_03.mp3",
                f"{base_url}/exercise_{i+1:02d}_option_04.mp3"
            ]
        
        return content

    def get_lesson_id(self, lesson_title: str) -> str:
        """Get lesson ID from Supabase"""
        url = f"{self.supabase_url}/rest/v1/lessons?title=eq.{lesson_title}&select=id"
        headers = {
            "apikey": self.supabase_key,
            "Authorization": f"Bearer {self.supabase_key}"
        }
        
        try:
            response = requests.get(url, headers=headers)
            if response.status_code == 200 and response.json():
                return response.json()[0]['id']
        except Exception as e:
            print(f"❌ Error getting lesson ID: {e}")
        
        return None

    def update_lesson_in_database(self, lesson_id: str, content: Dict[str, Any]) -> bool:
        """Update lesson in Supabase"""
        url = f"{self.supabase_url}/rest/v1/lessons?id=eq.{lesson_id}"
        headers = {
            "apikey": self.supabase_key,
            "Authorization": f"Bearer {self.supabase_key}",
            "Content-Type": "application/json",
            "Prefer": "return=minimal"
        }
        
        try:
            update_data = {"content_metadata": content}
            response = requests.patch(url, headers=headers, json=update_data)
            return response.status_code == 204
        except Exception as e:
            print(f"❌ Database update error: {e}")
            return False

    def process_lesson_properly(self, lesson_title: str) -> bool:
        """Process a lesson following the COMPLETE quality checklist"""
        
        print(f"\n{'='*60}")
        print(f"🎯 PROCESSING: {lesson_title}")
        print(f"📋 Following COMPREHENSIVE_QUALITY_CHECKLIST.md")
        print(f"{'='*60}")
        
        # Step 1: Get lesson ID
        print("📋 Step 1: Getting lesson ID...")
        lesson_id = self.get_lesson_id(lesson_title)
        if not lesson_id:
            print(f"❌ Lesson not found: {lesson_title}")
            return False
        print(f"✅ Found lesson ID: {lesson_id}")
        
        # Step 2: Generate content with Gemini Flash 2.0
        print("🤖 Step 2: Generating authentic content with Gemini Flash 2.0...")
        try:
            content = self.generate_with_gemini(lesson_title)
        except Exception as e:
            print(f"❌ Content generation failed: {e}")
            return False
        
        # Step 3: Validate with Claude (First validation)
        print("🔍 Step 3: Validating with Claude...")
        claude_validation = self.validate_with_claude(content, lesson_title)
        
        if not claude_validation.get('passes_validation', False):
            print(f"❌ Claude validation FAILED")
            print(f"Issues: {claude_validation.get('critical_issues', [])}")
            return False
        
        print("✅ Claude validation PASSED")
        
        # Step 4: Validate with GPT-4 (Second validation)
        print("🔍 Step 4: Validating with GPT-4 Turbo...")
        gpt4_validation = self.validate_with_gpt4(content, lesson_title)
        
        if not gpt4_validation.get('passes_validation', False):
            print(f"❌ GPT-4 validation FAILED")
            print(f"Issues: {gpt4_validation.get('critical_issues', [])}")
            return False
        
        print("✅ GPT-4 validation PASSED")
        
        # Step 5: Final quality check
        claude_score = claude_validation.get('overall_quality_score', 0)
        gpt4_score = gpt4_validation.get('overall_quality_score', 0)
        consensus_score = (claude_score + gpt4_score) / 2
        
        if consensus_score < 80:
            print(f"❌ Consensus score too low: {consensus_score}/100")
            return False
        
        print(f"✅ Consensus score: {consensus_score}/100")
        
        # Step 6: Update database
        print("💾 Step 6: Updating database...")
        success = self.update_lesson_in_database(lesson_id, content)
        
        if success:
            print(f"🎉 {lesson_title} COMPLETED SUCCESSFULLY!")
            print(f"📊 Quality Score: {consensus_score}/100")
            print(f"✅ Meets ALL quality checklist requirements")
            return True
        else:
            print(f"❌ Database update failed for {lesson_title}")
            return False

if __name__ == "__main__":
    generator = ProperLessonGenerator()

    # Test with a lesson that has placeholder content - fix it properly
    test_lesson = "Colors and Descriptions"
    print(f"🔧 FIXING PLACEHOLDER CONTENT: {test_lesson}")
    print("This lesson currently has generic template content - replacing with authentic Tamil content")

    success = generator.process_lesson_properly(test_lesson)

    if success:
        print(f"\n🎉 SUCCESS: {test_lesson} now has authentic Tamil content!")
        print("✅ Replaced placeholder content with real Tamil colors vocabulary")
        print("✅ Added authentic conversations about describing objects")
        print("✅ Included proper Tamil grammar for descriptions")
        print("✅ Created realistic exercises about colors and descriptions")
    else:
        print(f"\n❌ FAILED: {test_lesson} could not be fixed")
