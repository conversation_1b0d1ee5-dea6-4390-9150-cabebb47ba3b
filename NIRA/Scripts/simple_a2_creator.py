#!/usr/bin/env python3
"""
Simple A2 Lesson Creator for NIRA
Creates 30 A2 elementary level lessons with correct structure

This script creates the basic lesson structure that will be filled by our proven validators.
"""

import json
import requests
import time
from typing import Dict, List, Any

# Supabase configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# Tamil A2 Path ID (already exists)
TAMIL_A2_PATH_ID = "0b14776f-f0b1-4e65-8fac-40a4ce8f125b"

# A2 Elementary Tamil Lessons (30 lessons)
A2_LESSONS = [
    "Advanced Greetings and Social Interactions",
    "Extended Family and Community Relations", 
    "Complex Numbers and Mathematical Concepts",
    "Advanced Colors and Detailed Descriptions",
    "Restaurant Dining and Food Culture",
    "Medical Visits and Health Concerns",
    "Seasonal Activities and Weather Patterns",
    "Public Transportation and Travel Planning",
    "Fashion and Personal Style",
    "Household Chores and Daily Responsibilities",
    "Personal History and Life Events",
    "Apartment Hunting and Living Arrangements",
    "Weekly Schedules and Time Management",
    "Banking and Financial Services",
    "Giving Directions and Navigation",
    "Illness and Medical Emergencies",
    "Leisure Activities and Entertainment",
    "Career Development and Job Interviews",
    "Academic Studies and Learning Goals",
    "Digital Communication and Social Media",
    "Expressing Opinions and Preferences",
    "Cultural Events and Traditional Celebrations",
    "Environmental Awareness and Nature Conservation",
    "Cooking and Recipe Instructions",
    "Past Events and Future Plans",
    "City Transportation and Urban Life",
    "International Travel and Tourism",
    "Arts and Cultural Appreciation",
    "Historical Sites and Cultural Heritage",
    "Sports Participation and Physical Fitness"
]

class SimpleA2Creator:
    """Simple A2 lesson creator using correct database structure"""
    
    def __init__(self):
        self.headers = {
            'apikey': SUPABASE_KEY,
            'Authorization': f'Bearer {SUPABASE_KEY}',
            'Content-Type': 'application/json'
        }
    
    def create_lesson_data(self, title: str, sequence: int) -> Dict[str, Any]:
        """Create lesson data with correct structure"""
        return {
            'path_id': TAMIL_A2_PATH_ID,
            'title': title,
            'description': f'Elementary level Tamil lesson covering {title.lower()} with advanced vocabulary and grammar',
            'lesson_type': 'comprehensive',
            'difficulty_level': 2,  # A2 level
            'estimated_duration': 45,
            'sequence_order': sequence,
            'learning_objectives': [
                f'Master elementary vocabulary related to {title.lower()}',
                f'Engage in complex conversations about {title.lower()}',
                f'Apply advanced grammar rules in {title.lower()} context',
                f'Complete varied exercises testing {title.lower()} knowledge'
            ],
            'vocabulary_focus': None,
            'grammar_concepts': [],
            'cultural_notes': '',
            'prerequisite_lessons': [],
            'content_metadata': {
                'title': title,
                'description': f'Elementary level Tamil lesson covering {title.lower()}',
                'vocabulary': [],  # Will be filled by vocabulary validator
                'conversations': [],  # Will be filled by conversation validator
                'grammar_points': [],  # Will be filled by grammar validator
                'exercises': [],  # Will be filled by exercises validator
                'estimated_duration': 45,
                'learning_objectives': [
                    f'Master elementary vocabulary related to {title.lower()}',
                    f'Engage in complex conversations about {title.lower()}',
                    f'Apply advanced grammar rules in {title.lower()} context',
                    f'Complete varied exercises testing {title.lower()} knowledge'
                ]
            },
            'is_active': True,
            'audio_url': None,
            'audio_metadata': {},
            'has_audio': False
        }
    
    def create_all_a2_lessons(self) -> Dict[str, Any]:
        """Create all 30 A2 lessons"""
        print("🚀 SIMPLE A2 LESSON CREATOR")
        print("Creating 30 elementary Tamil lessons")
        print("=" * 50)
        
        results = {
            'total_lessons': len(A2_LESSONS),
            'created': 0,
            'failed': [],
            'lesson_ids': []
        }
        
        for i, lesson_title in enumerate(A2_LESSONS, 1):
            print(f"\n📖 Creating {i}/{len(A2_LESSONS)}: {lesson_title}")
            
            # Create lesson data
            lesson_data = self.create_lesson_data(lesson_title, i)
            
            try:
                # Insert lesson into database
                response = requests.post(f"{SUPABASE_URL}/rest/v1/lessons", json=lesson_data, headers=self.headers)
                
                if response.status_code == 201:
                    lesson_id = response.json()[0]['id']
                    results['lesson_ids'].append(lesson_id)
                    results['created'] += 1
                    print(f"✅ Created: {lesson_title}")
                else:
                    results['failed'].append(f"{lesson_title}: {response.status_code}")
                    print(f"❌ Failed: {lesson_title} - {response.status_code}")
                    print(f"Response: {response.text}")
                    
            except Exception as e:
                results['failed'].append(f"{lesson_title}: {str(e)}")
                print(f"❌ Error: {lesson_title} - {e}")
            
            # Rate limiting
            time.sleep(1)
        
        return results
    
    def display_creation_summary(self, results: Dict[str, Any]) -> None:
        """Display lesson creation summary"""
        print(f"\n📊 A2 LESSON CREATION SUMMARY:")
        print("=" * 50)
        print(f"  • Path ID: {TAMIL_A2_PATH_ID}")
        print(f"  • Total Lessons: {results.get('total_lessons', 0)}")
        print(f"  • Successfully Created: {results.get('created', 0)}")
        print(f"  • Failed: {len(results.get('failed', []))}")
        
        if results.get('failed'):
            print(f"\n❌ FAILED LESSONS:")
            for failure in results['failed']:
                print(f"  • {failure}")
        
        if results.get('created', 0) > 0:
            print(f"\n✅ SUCCESS! Created {results['created']} A2 lessons")
            print(f"\n📋 NEXT STEPS:")
            print("1. ✅ A2 lesson structure created")
            print("2. 🔄 Run A2 vocabulary validator")
            print("3. 🔄 Run A2 conversation validator")
            print("4. 🔄 Run A2 grammar validator")
            print("5. 🔄 Run A2 exercises validator")
            print("6. 🎵 Generate A2 audio content")
            print("7. 📱 Test A2 lessons in iOS app")

def main():
    """Main function"""
    print("🎯 SIMPLE TAMIL A2 ELEMENTARY LESSON CREATOR")
    print("Building on proven A1 foundation")
    print("=" * 50)
    
    creator = SimpleA2Creator()
    
    # Create all A2 lessons
    results = creator.create_all_a2_lessons()
    
    # Display summary
    creator.display_creation_summary(results)
    
    # Save path ID for validators
    with open('/tmp/a2_path_id.txt', 'w') as f:
        f.write(TAMIL_A2_PATH_ID)
    print(f"\n💾 A2 Path ID saved for validators: {TAMIL_A2_PATH_ID}")

if __name__ == "__main__":
    main()
