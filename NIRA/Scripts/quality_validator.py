#!/usr/bin/env python3
"""
Quality Validator - Dual validation with <PERSON> and GPT-4 Turbo
Validates lesson content against comprehensive quality checklist
"""

import requests
import json
import openai
from typing import Dict, Any, List
import anthropic

class QualityValidator:
    def __init__(self, claude_key: str, openai_key: str):
        self.claude_client = anthropic.Anthropic(api_key=claude_key)
        self.openai_client = openai.OpenAI(api_key=openai_key)
    
    def validate_with_claude(self, content: Dict[str, Any], lesson_title: str) -> Dict[str, Any]:
        """Validate content quality with <PERSON>"""
        
        validation_prompt = f"""
Validate this Tamil A1 lesson content for "{lesson_title}" against these quality standards:

CONTENT TO VALIDATE:
{json.dumps(content, indent=2, ensure_ascii=False)}

VALIDATION CHECKLIST:
1. CONTENT COUNTS:
   - Exactly 25 unique vocabulary items
   - Exactly 15 unique conversation scenarios
   - Exactly 10 unique grammar points
   - At least 12 unique exercises

2. UNIQUENESS CHECK:
   - No repeated vocabulary words
   - No repeated conversation titles/content
   - No repeated exercise questions
   - All content unique within lesson

3. PRONUNCIATION COMPLETENESS:
   - All exercises have options_pronunciations array
   - Pronunciation arrays match option arrays in length
   - No empty pronunciations

4. CULTURAL AUTHENTICITY:
   - Content reflects authentic Tamil culture
   - Examples use realistic scenarios
   - Appropriate for Tamil learners
   - No cultural stereotypes

5. A1 DIFFICULTY LEVEL:
   - Vocabulary appropriate for beginners
   - Grammar concepts basic level
   - Sentence structures simple
   - No advanced concepts

6. TECHNICAL REQUIREMENTS:
   - Proper Tamil script
   - Accurate romanization
   - Complete translations
   - No placeholder content

Return JSON with validation results:
{{
  "vocabulary_count_valid": boolean,
  "conversations_count_valid": boolean,
  "grammar_count_valid": boolean,
  "exercises_count_valid": boolean,
  "vocabulary_unique": boolean,
  "conversations_unique": boolean,
  "exercises_unique": boolean,
  "pronunciations_complete": boolean,
  "cultural_authentic": boolean,
  "a1_appropriate": boolean,
  "technical_correct": boolean,
  "overall_quality_score": number (0-100),
  "issues_found": ["list of specific issues"],
  "recommendations": ["list of improvements"]
}}
"""

        try:
            response = self.claude_client.messages.create(
                model="claude-3-5-sonnet-20241022",
                max_tokens=2000,
                messages=[{"role": "user", "content": validation_prompt}]
            )
            
            result_text = response.content[0].text
            
            # Parse JSON response
            if "```json" in result_text:
                result_text = result_text.split("```json")[1].split("```")[0]
            
            validation_result = json.loads(result_text)
            return validation_result
            
        except Exception as e:
            print(f"❌ Claude validation failed: {e}")
            return self._default_validation_result()
    
    def validate_with_gpt4(self, content: Dict[str, Any], lesson_title: str) -> Dict[str, Any]:
        """Validate content quality with GPT-4 Turbo"""
        
        validation_prompt = f"""
As a Tamil language expert, validate this A1 lesson content for "{lesson_title}":

{json.dumps(content, indent=2, ensure_ascii=False)}

Check for:
1. Content accuracy and authenticity
2. Grammar correctness
3. Realistic examples and scenarios
4. Appropriate difficulty for A1 learners
5. No placeholder or generic content
6. Proper Tamil script and romanization

Return JSON validation results with boolean flags and specific feedback.
"""

        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-4-turbo",
                messages=[
                    {"role": "system", "content": "You are a Tamil language expert validating lesson content."},
                    {"role": "user", "content": validation_prompt}
                ],
                max_tokens=2000,
                temperature=0.1
            )
            
            result_text = response.choices[0].message.content
            
            # Parse JSON response
            if "```json" in result_text:
                result_text = result_text.split("```json")[1].split("```")[0]
            
            validation_result = json.loads(result_text)
            return validation_result
            
        except Exception as e:
            print(f"❌ GPT-4 validation failed: {e}")
            return self._default_validation_result()
    
    def _default_validation_result(self) -> Dict[str, Any]:
        """Default validation result if API fails"""
        return {
            "vocabulary_count_valid": False,
            "conversations_count_valid": False,
            "grammar_count_valid": False,
            "exercises_count_valid": False,
            "vocabulary_unique": False,
            "conversations_unique": False,
            "exercises_unique": False,
            "pronunciations_complete": False,
            "cultural_authentic": False,
            "a1_appropriate": False,
            "technical_correct": False,
            "overall_quality_score": 0,
            "issues_found": ["Validation API failed"],
            "recommendations": ["Retry validation"]
        }
    
    def comprehensive_validation(self, content: Dict[str, Any], lesson_title: str) -> Dict[str, Any]:
        """Run comprehensive validation with both models"""
        
        print(f"🔍 Running comprehensive validation for {lesson_title}...")
        
        # Claude validation
        print("🤖 Claude validation...")
        claude_result = self.validate_with_claude(content, lesson_title)
        
        # GPT-4 validation  
        print("🤖 GPT-4 validation...")
        gpt4_result = self.validate_with_gpt4(content, lesson_title)
        
        # Combine results
        combined_result = {
            "claude_validation": claude_result,
            "gpt4_validation": gpt4_result,
            "consensus_score": self._calculate_consensus(claude_result, gpt4_result),
            "passed_validation": self._check_overall_pass(claude_result, gpt4_result),
            "critical_issues": self._identify_critical_issues(claude_result, gpt4_result)
        }
        
        return combined_result
    
    def _calculate_consensus(self, claude_result: Dict, gpt4_result: Dict) -> float:
        """Calculate consensus score between validators"""
        claude_score = claude_result.get("overall_quality_score", 0)
        gpt4_score = gpt4_result.get("overall_quality_score", 0)
        
        # Average the scores
        return (claude_score + gpt4_score) / 2
    
    def _check_overall_pass(self, claude_result: Dict, gpt4_result: Dict) -> bool:
        """Check if content passes overall validation"""
        
        # Critical checks that must pass
        critical_checks = [
            "vocabulary_count_valid",
            "conversations_count_valid", 
            "exercises_count_valid",
            "pronunciations_complete",
            "vocabulary_unique",
            "conversations_unique",
            "exercises_unique"
        ]
        
        # Both validators must agree on critical checks
        for check in critical_checks:
            claude_pass = claude_result.get(check, False)
            gpt4_pass = gpt4_result.get(check, False)
            
            if not (claude_pass and gpt4_pass):
                return False
        
        # Quality score threshold
        consensus_score = self._calculate_consensus(claude_result, gpt4_result)
        
        return consensus_score >= 80
    
    def _identify_critical_issues(self, claude_result: Dict, gpt4_result: Dict) -> List[str]:
        """Identify critical issues that need fixing"""
        
        issues = []
        
        # Combine issues from both validators
        claude_issues = claude_result.get("issues_found", [])
        gpt4_issues = gpt4_result.get("issues_found", [])
        
        all_issues = claude_issues + gpt4_issues
        
        # Remove duplicates
        unique_issues = list(set(all_issues))
        
        return unique_issues
    
    def quick_validation(self, content: Dict[str, Any]) -> Dict[str, bool]:
        """Quick local validation checks"""
        
        return {
            "has_vocabulary": len(content.get("vocabulary", [])) == 25,
            "has_conversations": len(content.get("conversations", [])) == 15,
            "has_grammar": len(content.get("grammar_points", [])) == 10,
            "has_exercises": len(content.get("exercises", [])) >= 12,
            "exercises_have_pronunciations": all(
                "options_pronunciations" in ex 
                for ex in content.get("exercises", [])
            ),
            "no_empty_content": all([
                content.get("vocabulary"),
                content.get("conversations"),
                content.get("grammar_points"),
                content.get("exercises")
            ])
        }

if __name__ == "__main__":
    # Test the validator
    claude_key = "sk-ant-..."  # Replace with actual key
    openai_key = "sk-proj-..."  # Replace with actual key
    
    validator = QualityValidator(claude_key, openai_key)
    
    # Test with sample content
    test_content = {
        "vocabulary": [{"word": "test"}] * 25,
        "conversations": [{"title": f"conv {i}"}] * 15,
        "grammar_points": [{"rule": f"rule {i}"}] * 10,
        "exercises": [{"question": f"q {i}", "options_pronunciations": ["a", "b", "c", "d"]}] * 12
    }
    
    result = validator.comprehensive_validation(test_content, "Test Lesson")
    print(f"Validation passed: {result['passed_validation']}")
    print(f"Consensus score: {result['consensus_score']}")
