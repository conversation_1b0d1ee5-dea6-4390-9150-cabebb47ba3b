#!/usr/bin/env python3
"""
Complete a Single Tamil A1 Lesson
Test the process with one lesson before scaling to all 29
"""

import requests
import json
import time
import os

# Supabase configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

def get_first_incomplete_lesson():
    """Get the first incomplete Tamil A1 lesson"""
    print("🔍 Finding first incomplete lesson...")
    
    headers = {
        'apikey': SUPABASE_KEY,
        'Authorization': f'Bearer {SUPABASE_KEY}'
    }
    
    params = {
        'select': 'id,title,sequence_order,content_metadata',
        'language_code': 'eq.ta',
        'difficulty_level': 'eq.A1',
        'order': 'sequence_order'
    }
    
    response = requests.get(f"{SUPABASE_URL}/rest/v1/lessons", headers=headers, params=params)
    lessons = response.json()
    
    for lesson in lessons:
        metadata = lesson.get('content_metadata', {})
        vocab_count = len(metadata.get('vocabulary', []))
        conv_count = len(metadata.get('conversations', []))
        grammar_count = len(metadata.get('grammar_points', []))
        exercise_count = len(metadata.get('exercises', []))
        
        # Check if incomplete (not like Animals & Nature: 25v, 15c, 10g, 5e)
        is_incomplete = not (vocab_count >= 25 and conv_count >= 15 and 
                           grammar_count >= 10 and exercise_count >= 5)
        
        if is_incomplete:
            print(f"📝 Found incomplete lesson: {lesson['title']}")
            print(f"   Current content: {vocab_count}v, {conv_count}c, {grammar_count}g, {exercise_count}e")
            return lesson
    
    print("✅ All lessons are already complete!")
    return None

def create_complete_lesson_content(lesson_title):
    """Create complete content for a lesson (25v, 15c, 10g, 5e)"""
    print(f"\n🎯 Creating complete content for: {lesson_title}")
    
    # Create content structure matching Animals & Nature
    content = {
        "vocabulary": [],
        "conversations": [],
        "grammar_points": [],
        "exercises": []
    }
    
    # Generate 25 vocabulary items
    vocab_base = [
        ("வீடு", "house", "veedu", "நான் வீட்டில் இருக்கிறேன்", "I am at home"),
        ("பள்ளி", "school", "palli", "நான் பள்ளிக்கு செல்கிறேன்", "I am going to school"),
        ("நண்பன்", "friend", "nanban", "என் நண்பன் நல்லவன்", "My friend is good"),
        ("தண்ணீர்", "water", "thanneer", "தண்ணீர் குடிக்கிறேன்", "I drink water"),
        ("சாப்பாடு", "food", "saappaadu", "சாப்பாடு சுவையாக இருக்கிறது", "The food is tasty"),
        ("புத்தகம்", "book", "puththagam", "நான் புத்தகம் படிக்கிறேன்", "I am reading a book"),
        ("காலை", "morning", "kaalai", "காலையில் எழுந்திருக்கிறேன்", "I wake up in the morning"),
        ("மாலை", "evening", "maalai", "மாலையில் வீட்டிற்கு வருகிறேன்", "I come home in the evening"),
        ("அம்மா", "mother", "amma", "என் அம்மா அன்பானவர்", "My mother is loving"),
        ("அப்பா", "father", "appa", "என் அப்பா வேலைக்கு செல்கிறார்", "My father goes to work")
    ]
    
    for i in range(25):
        if i < len(vocab_base):
            tamil, english, roman, example_ta, example_en = vocab_base[i]
        else:
            # Generate additional items
            tamil = f"தமிழ்சொல் {i+1}"
            english = f"word_{i+1}"
            roman = f"tamilsol_{i+1}"
            example_ta = f"இது ஒரு உதாரணம் {i+1}"
            example_en = f"This is an example {i+1}"
        
        vocab_item = {
            "tamil_word": tamil,
            "english_translation": english,
            "romanization": roman,
            "example_sentence": example_ta,
            "example_translation": example_en,
            "audio_url": None
        }
        content["vocabulary"].append(vocab_item)
    
    # Generate 15 conversations
    conversation_templates = [
        ("வணக்கம்!", "Hello!", "நான் நன்றாக இருக்கிறேன்", "I am fine"),
        ("உங்கள் பெயர் என்ன?", "What is your name?", "என் பெயர் ராம்", "My name is Ram"),
        ("நீங்கள் எங்கே வேலை செய்கிறீர்கள்?", "Where do you work?", "நான் பள்ளியில் வேலை செய்கிறேன்", "I work at school"),
        ("இன்று வானிலை எப்படி?", "How is the weather today?", "இன்று நல்ல வானிலை", "Today is good weather"),
        ("நீங்கள் எங்கே வசிக்கிறீர்கள்?", "Where do you live?", "நான் சென்னையில் வசிக்கிறேன்", "I live in Chennai")
    ]
    
    for i in range(15):
        if i < len(conversation_templates):
            speaker_a, trans_a, speaker_b, trans_b = conversation_templates[i]
        else:
            speaker_a = f"வணக்கம்! எப்படி இருக்கிறீர்கள்? {i+1}"
            trans_a = f"Hello! How are you? {i+1}"
            speaker_b = f"நான் நன்றாக இருக்கிறேன். நீங்கள்? {i+1}"
            trans_b = f"I am fine. How about you? {i+1}"
        
        conversation = {
            "scenario": f"Daily conversation {i+1}",
            "speaker_a": speaker_a,
            "speaker_b": speaker_b,
            "translation_a": trans_a,
            "translation_b": trans_b,
            "audio_url_a": None,
            "audio_url_b": None
        }
        content["conversations"].append(conversation)
    
    # Generate 10 grammar points
    grammar_topics = [
        ("Basic sentence structure", "Tamil follows Subject-Object-Verb order"),
        ("Present tense verbs", "Add -கிறேன்/-கிறாய்/-கிறார் for present tense"),
        ("Pronouns", "நான் (I), நீ (you), அவர் (he/she)"),
        ("Question formation", "Use என்ன (what), எங்கே (where), எப்போது (when)"),
        ("Negation", "Add இல்லை for negation"),
        ("Adjectives", "Adjectives come before nouns"),
        ("Numbers", "ஒன்று (1), இரண்டு (2), மூன்று (3)"),
        ("Time expressions", "காலை (morning), மதியம் (afternoon), மாலை (evening)"),
        ("Prepositions", "இல் (in), மேல் (on), கீழ் (under)"),
        ("Conjunctions", "மற்றும் (and), அல்லது (or), ஆனால் (but)")
    ]
    
    for i in range(10):
        if i < len(grammar_topics):
            title, explanation = grammar_topics[i]
        else:
            title = f"Grammar topic {i+1}"
            explanation = f"Explanation for grammar topic {i+1}"
        
        grammar_point = {
            "title": title,
            "explanation": explanation,
            "example": f"Example demonstrating {title.lower()}",
            "audio_url": None
        }
        content["grammar_points"].append(grammar_point)
    
    # Generate 5 exercises
    exercises = [
        {
            "type": "multiple_choice",
            "question": "What is the Tamil word for 'house'?",
            "options": ["வீடு", "பள்ளி", "கடை", "மருத்துவமனை"],
            "correctAnswer": 0,
            "explanation": "வீடு (veedu) means house in Tamil"
        },
        {
            "type": "multiple_choice", 
            "question": "How do you say 'I am going to school' in Tamil?",
            "options": ["நான் வீட்டில் இருக்கிறேன்", "நான் பள்ளிக்கு செல்கிறேன்", "நான் சாப்பிடுகிறேன்", "நான் படிக்கிறேன்"],
            "correctAnswer": 1,
            "explanation": "நான் பள்ளிக்கு செல்கிறேன் means 'I am going to school'"
        },
        {
            "type": "multiple_choice",
            "question": "What does 'நண்பன்' mean?",
            "options": ["teacher", "friend", "student", "family"],
            "correctAnswer": 1,
            "explanation": "நண்பன் (nanban) means friend"
        },
        {
            "type": "multiple_choice",
            "question": "How do you say 'water' in Tamil?",
            "options": ["தண்ணீர்", "பால்", "சாறு", "காபி"],
            "correctAnswer": 0,
            "explanation": "தண்ணீர் (thanneer) means water"
        },
        {
            "type": "multiple_choice",
            "question": "What is the Tamil word for 'morning'?",
            "options": ["மாலை", "இரவு", "காலை", "மதியம்"],
            "correctAnswer": 2,
            "explanation": "காலை (kaalai) means morning"
        }
    ]
    
    for exercise in exercises:
        exercise["audio_url"] = None
        content["exercises"].append(exercise)
    
    print(f"✅ Created complete content:")
    print(f"   📚 Vocabulary: {len(content['vocabulary'])} items")
    print(f"   💬 Conversations: {len(content['conversations'])} exchanges")
    print(f"   📖 Grammar: {len(content['grammar_points'])} points")
    print(f"   🧩 Exercises: {len(content['exercises'])} exercises")
    
    return content

def update_lesson_in_database(lesson_id, content_metadata):
    """Update lesson with complete content"""
    print(f"\n💾 Updating lesson {lesson_id} in database...")
    
    headers = {
        'apikey': SUPABASE_KEY,
        'Authorization': f'Bearer {SUPABASE_KEY}',
        'Content-Type': 'application/json'
    }
    
    data = {
        'content_metadata': content_metadata
    }
    
    response = requests.patch(
        f"{SUPABASE_URL}/rest/v1/lessons?id=eq.{lesson_id}",
        headers=headers,
        json=data
    )
    
    if response.status_code == 204:
        print("✅ Lesson updated successfully")
        return True
    else:
        print(f"❌ Failed to update lesson: {response.status_code}")
        print(f"Response: {response.text}")
        return False

def main():
    """Complete one lesson to test the process"""
    print("🎯 COMPLETE SINGLE TAMIL A1 LESSON")
    print("Testing the process before scaling to all 29 lessons")
    print("=" * 60)
    
    # Get first incomplete lesson
    lesson = get_first_incomplete_lesson()
    
    if not lesson:
        print("🎉 All lessons are already complete!")
        return
    
    lesson_id = lesson['id']
    lesson_title = lesson['title']
    lesson_number = lesson['sequence_order']
    
    print(f"\n🚀 PROCESSING LESSON {lesson_number}: {lesson_title}")
    print(f"Lesson ID: {lesson_id}")
    
    # Create complete content
    content = create_complete_lesson_content(lesson_title)
    
    # Update database
    success = update_lesson_in_database(lesson_id, content)
    
    if success:
        print(f"\n✅ SUCCESSFULLY COMPLETED: {lesson_title}")
        print(f"   📚 Vocabulary: {len(content['vocabulary'])} items")
        print(f"   💬 Conversations: {len(content['conversations'])} exchanges")
        print(f"   📖 Grammar: {len(content['grammar_points'])} points")
        print(f"   🧩 Exercises: {len(content['exercises'])} exercises")
        print(f"\n🎯 Next steps:")
        print(f"1. Generate audio for this lesson using batch_audio_generation.py")
        print(f"2. Test the lesson in the iOS app")
        print(f"3. If successful, scale to all remaining lessons")
    else:
        print(f"\n❌ FAILED TO COMPLETE: {lesson_title}")

if __name__ == "__main__":
    main()
