#!/usr/bin/env python3
"""
Automated Lesson Pipeline - Scalable A1 Tamil Lesson Generator
Uses Gemini Flash 2.0 + dual validation (Claude + GPT-4 Turbo)
"""

import requests
import json
import time
import os
from typing import Dict, List, Any

class LessonPipeline:
    def __init__(self):
        # API Keys
        self.gemini_key = "AIzaSyBVZ_5-2Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8"  # Replace with actual
        self.openai_key = "sk-proj-..."  # Replace with actual
        self.claude_key = "sk-ant-..."  # Replace with actual
        
        # Supabase config
        self.supabase_url = "https://lyaojebttnqilmdosmjk.supabase.co"
        self.supabase_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"
        
        # Lesson requirements
        self.requirements = {
            "vocabulary": 25,
            "conversations": 15,
            "grammar_points": 10,
            "exercises": 12
        }
        
        # A1 Tamil lessons to process
        self.lessons = [
            "Basic Greetings and Introductions",
            "Body Parts and Health", 
            "Clothing and Shopping",
            "Colors and Description",
            "Common Verbs and Actions",
            "Daily Routines and Activities",
            "Directions and Locations",
            "Education and Learning",
            "Emotions and Feelings",
            "Family and Relationships",
            "Food and Cooking",
            "Hobbies and Interests",
            "House and Home",
            "Music and Movies",
            "Numbers and Counting",
            "Occupations and Work",
            "Sports and Exercise",
            "Technology and Communication",
            "Time and Calendar",
            "Transportation and Travel",
            "Weather and Seasons",
            "Animals and Nature",
            "Festivals and Celebrations",
            "Famous Landmarks",
            "Vegetables",
            "Days Weeks Months",
            "Local Transportation",
            "Travel and Tourism",
            "Basic Shopping",
            "Cultural Traditions"
        ]

    def get_lesson_id(self, lesson_title: str) -> str:
        """Get lesson ID from Supabase"""
        url = f"{self.supabase_url}/rest/v1/lessons?title=eq.{lesson_title}&select=id"
        headers = {
            "apikey": self.supabase_key,
            "Authorization": f"Bearer {self.supabase_key}"
        }
        
        response = requests.get(url, headers=headers)
        if response.status_code == 200 and response.json():
            return response.json()[0]['id']
        return None

    def generate_content_with_gemini(self, lesson_title: str) -> Dict[str, Any]:
        """Generate lesson content using Gemini Flash 2.0"""
        prompt = f"""
Create a complete A1 Tamil lesson for "{lesson_title}" with exactly:

VOCABULARY (25 items):
- Tamil word, English translation, pronunciation, example sentence with translation
- All culturally authentic and A1 appropriate

CONVERSATIONS (15 scenarios):
- Each with title, 2-3 exchanges between speakers
- Tamil text, English translation, pronunciation for each exchange
- Realistic scenarios related to {lesson_title}

GRAMMAR (10 points):
- Rule explanation, 3 examples with pronunciations, cultural tips
- Progressive difficulty within A1 level

EXERCISES (12 items):
- Multiple choice questions testing lesson content
- 4 options each with Tamil/English pronunciations
- Explanations for correct answers

Format as valid JSON with proper Tamil script and romanized pronunciations.
Ensure NO repeated content - all items must be unique.
"""

        # Gemini API call (placeholder - implement actual API)
        # For now, return structured template
        return self._create_lesson_template(lesson_title)

    def _create_lesson_template(self, lesson_title: str) -> Dict[str, Any]:
        """Create lesson template (placeholder for Gemini output)"""
        base_url = f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/{lesson_title.lower().replace(' ', '_')}"
        
        return {
            "vocabulary": self._generate_vocabulary(lesson_title, base_url),
            "conversations": self._generate_conversations(lesson_title, base_url),
            "grammar_points": self._generate_grammar(lesson_title, base_url),
            "exercises": self._generate_exercises(lesson_title, base_url)
        }

    def _generate_vocabulary(self, lesson_title: str, base_url: str) -> List[Dict]:
        """Generate vocabulary items"""
        # This would be replaced by Gemini output
        vocab_templates = {
            "Clothing and Shopping": [
                {"word": "உடை", "translation": "Clothes", "pronunciation": "udai"},
                {"word": "சட்டை", "translation": "Shirt", "pronunciation": "sattai"},
                {"word": "பாவாடை", "translation": "Skirt", "pronunciation": "paavaadai"},
                # ... 22 more items
            ]
        }
        
        vocab_list = vocab_templates.get(lesson_title, [])
        
        # Fill to 25 items with proper structure
        vocabulary = []
        for i, item in enumerate(vocab_list[:25]):
            vocabulary.append({
                "word": item["word"],
                "translation": item["translation"], 
                "pronunciation": item["pronunciation"],
                "example": f"{item['word']} அழகாக இருக்கிறது ({item['pronunciation']} azhaagaaga irukkiRadhu) - The {item['translation'].lower()} is beautiful",
                "difficulty": "basic",
                "part_of_speech": "noun",
                "word_audio_url": f"{base_url}/vocab_{i+1:02d}_word.mp3",
                "example_audio_url": f"{base_url}/vocab_{i+1:02d}_example.mp3"
            })
        
        return vocabulary

    def _generate_conversations(self, lesson_title: str, base_url: str) -> List[Dict]:
        """Generate conversation scenarios"""
        # This would be replaced by Gemini output
        conversations = []
        for i in range(15):
            conversations.append({
                "title": f"{lesson_title} Scenario {i+1}",
                "scenario": f"Conversation about {lesson_title.lower()}",
                "exchanges": [
                    {
                        "text": "வணக்கம்!",
                        "speaker": "Person A",
                        "translation": "Hello!",
                        "pronunciation": "vanakkam!",
                        "audio_url": f"{base_url}/conv_{i+1:02d}_01.mp3"
                    },
                    {
                        "text": "வணக்கம்! எப்படி இருக்கிறீர்கள்?",
                        "speaker": "Person B", 
                        "translation": "Hello! How are you?",
                        "pronunciation": "vanakkam! eppaddi irukkiReerkal?",
                        "audio_url": f"{base_url}/conv_{i+1:02d}_02.mp3"
                    }
                ],
                "difficulty": "beginner"
            })
        
        return conversations

    def _generate_grammar(self, lesson_title: str, base_url: str) -> List[Dict]:
        """Generate grammar points"""
        # This would be replaced by Gemini output
        grammar_points = []
        for i in range(10):
            grammar_points.append({
                "rule": f"{lesson_title} Grammar Rule {i+1}",
                "explanation": f"Grammar explanation for {lesson_title}",
                "examples": [
                    f"Example 1 for rule {i+1}",
                    f"Example 2 for rule {i+1}",
                    f"Example 3 for rule {i+1}"
                ],
                "tips": f"Cultural tip for {lesson_title}",
                "examples_audio_urls": [
                    f"{base_url}/grammar_{i+1:02d}_01.mp3",
                    f"{base_url}/grammar_{i+1:02d}_02.mp3", 
                    f"{base_url}/grammar_{i+1:02d}_03.mp3"
                ]
            })
        
        return grammar_points

    def _generate_exercises(self, lesson_title: str, base_url: str) -> List[Dict]:
        """Generate exercise questions"""
        # This would be replaced by Gemini output
        exercises = []
        for i in range(12):
            exercises.append({
                "type": "multiple_choice",
                "points": 10,
                "question": f"Question {i+1} about {lesson_title}?",
                "options": ["Option 1", "Option 2", "Option 3", "Option 4"],
                "options_pronunciations": ["option 1", "option 2", "option 3", "option 4"],
                "correctAnswer": 0,
                "explanation": f"Explanation for question {i+1}",
                "options_audio_urls": [
                    f"{base_url}/exercise_{i+1:02d}_option_01.mp3",
                    f"{base_url}/exercise_{i+1:02d}_option_02.mp3",
                    f"{base_url}/exercise_{i+1:02d}_option_03.mp3",
                    f"{base_url}/exercise_{i+1:02d}_option_04.mp3"
                ]
            })
        
        return exercises

    def validate_with_claude(self, content: Dict[str, Any], lesson_title: str) -> Dict[str, Any]:
        """Validate content quality with Claude"""
        # Claude API validation (placeholder)
        print(f"🔍 Claude validation for {lesson_title}...")
        
        # Check for duplicates, quality, cultural authenticity
        validation_results = {
            "vocabulary_unique": len(set(item["word"] for item in content["vocabulary"])) == 25,
            "conversations_unique": len(set(conv["title"] for conv in content["conversations"])) == 15,
            "exercises_unique": len(set(ex["question"] for ex in content["exercises"])) == 12,
            "pronunciations_complete": all("options_pronunciations" in ex for ex in content["exercises"]),
            "cultural_authentic": True,  # Claude would validate this
            "a1_appropriate": True  # Claude would validate this
        }
        
        return validation_results

    def validate_with_gpt4(self, content: Dict[str, Any], lesson_title: str) -> Dict[str, Any]:
        """Validate content quality with GPT-4 Turbo"""
        # GPT-4 API validation (placeholder)
        print(f"🔍 GPT-4 validation for {lesson_title}...")
        
        # Second opinion on quality
        validation_results = {
            "content_accuracy": True,
            "grammar_correct": True,
            "examples_realistic": True,
            "difficulty_appropriate": True,
            "no_placeholders": True
        }
        
        return validation_results

    def update_lesson_in_database(self, lesson_id: str, content: Dict[str, Any]) -> bool:
        """Update lesson in Supabase"""
        url = f"{self.supabase_url}/rest/v1/lessons?id=eq.{lesson_id}"
        headers = {
            "apikey": self.supabase_key,
            "Authorization": f"Bearer {self.supabase_key}",
            "Content-Type": "application/json",
            "Prefer": "return=minimal"
        }
        
        update_data = {"content_metadata": content}
        response = requests.patch(url, headers=headers, json=update_data)
        
        return response.status_code == 204

    def process_single_lesson(self, lesson_title: str) -> bool:
        """Process a single lesson through the complete pipeline"""
        print(f"\n🎯 Processing: {lesson_title}")
        
        # Step 1: Get lesson ID
        lesson_id = self.get_lesson_id(lesson_title)
        if not lesson_id:
            print(f"❌ Lesson not found: {lesson_title}")
            return False
        
        # Step 2: Generate content with Gemini
        print("🤖 Generating content with Gemini Flash 2.0...")
        content = self.generate_content_with_gemini(lesson_title)
        
        # Step 3: Validate with Claude
        claude_validation = self.validate_with_claude(content, lesson_title)
        if not all(claude_validation.values()):
            print(f"❌ Claude validation failed: {claude_validation}")
            return False
        
        # Step 4: Validate with GPT-4
        gpt4_validation = self.validate_with_gpt4(content, lesson_title)
        if not all(gpt4_validation.values()):
            print(f"❌ GPT-4 validation failed: {gpt4_validation}")
            return False
        
        # Step 5: Update database
        print("💾 Updating database...")
        success = self.update_lesson_in_database(lesson_id, content)
        
        if success:
            print(f"✅ {lesson_title} completed successfully!")
            return True
        else:
            print(f"❌ Database update failed for {lesson_title}")
            return False

    def process_all_lessons(self):
        """Process all 30 A1 Tamil lessons"""
        print("🚀 Starting automated lesson pipeline for 30 A1 Tamil lessons...")
        
        completed = 0
        failed = []
        
        for i, lesson_title in enumerate(self.lessons, 1):
            print(f"\n📊 Progress: {i}/30 lessons")
            
            success = self.process_single_lesson(lesson_title)
            
            if success:
                completed += 1
            else:
                failed.append(lesson_title)
            
            # Brief pause between lessons
            time.sleep(2)
        
        # Final report
        print(f"\n🎉 Pipeline completed!")
        print(f"✅ Completed: {completed}/30 lessons")
        print(f"❌ Failed: {len(failed)} lessons")
        
        if failed:
            print(f"Failed lessons: {', '.join(failed)}")
        
        return completed == 30

if __name__ == "__main__":
    pipeline = LessonPipeline()
    pipeline.process_all_lessons()
