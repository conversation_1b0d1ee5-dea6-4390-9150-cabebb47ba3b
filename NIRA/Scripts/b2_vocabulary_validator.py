#!/usr/bin/env python3
"""
B2 Vocabulary Validator for NIRA
B2 Upper-Intermediate level Tamil lessons

This script creates and validates B2 upper-intermediate level vocabulary:
- 40 vocabulary items per lesson
- Advanced vocabulary with specialized terms
- Complex abstract concepts
- Upper-intermediate level difficulty
"""

import json
import requests
import time
from typing import Dict, List, Any
import google.generativeai as genai

# API Configuration
GEMINI_API_KEY = "AIzaSyAYasSocME9nPr7HP625EEx4cofwm5MH3Q"
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# Initialize Gemini
genai.configure(api_key=GEMINI_API_KEY)
gemini_model = genai.GenerativeModel('gemini-2.0-flash-exp')

# Tamil B2 Path ID
TAMIL_B2_PATH_ID = "b2-upper-intermediate-path-id"

# B2 Upper-Intermediate Tamil Lessons (30 lessons)
B2_LESSONS = [
    "Advanced Diplomatic and International Relations",
    "Corporate Strategy and Executive Leadership",
    "Quantum Physics and Advanced Mathematics",
    "Psychological Analysis and Behavioral Studies",
    "Molecular Gastronomy and Culinary Science",
    "Advanced Medical Research and Biotechnology",
    "Global Climate Policy and Environmental Law",
    "Aerospace Engineering and Space Technology",
    "Haute Couture and Fashion Theory",
    "Architectural Design and Urban Planning",
    "Philosophical Discourse and Ethical Debates",
    "International Real Estate and Investment",
    "Strategic Management and Organizational Behavior",
    "Macroeconomics and Financial Markets",
    "Smart Cities and Technological Infrastructure",
    "Disaster Management and Risk Assessment",
    "Film Theory and Cinematic Arts",
    "Executive Coaching and Leadership Development",
    "Academic Research and Scholarly Writing",
    "Artificial Intelligence and Machine Learning",
    "Constitutional Law and Judicial Systems",
    "Anthropological Studies and Cultural Theory",
    "Renewable Energy and Sustainable Technology",
    "Advanced Culinary Arts and Food Science",
    "Geopolitical Analysis and Strategic Forecasting",
    "Transportation Engineering and Logistics",
    "International Trade and Economic Policy",
    "Contemporary Philosophy and Critical Theory",
    "Heritage Conservation and Museum Studies",
    "Professional Athletics and Sports Science"
]

class B2VocabularyValidator:
    """B2 vocabulary validator and creator"""
    
    def __init__(self):
        self.headers = {
            'apikey': SUPABASE_KEY,
            'Authorization': f'Bearer {SUPABASE_KEY}',
            'Content-Type': 'application/json'
        }
    
    def create_b2_lesson_with_vocabulary(self, title: str, sequence: int) -> bool:
        """Create B2 lesson with vocabulary directly"""
        print(f"\n🔧 CREATING B2 LESSON: {title}")
        
        vocabulary = self.generate_b2_vocabulary(title)
        
        if not vocabulary:
            print(f"❌ Failed to generate vocabulary for {title}")
            return False
        
        lesson_data = {
            'path_id': TAMIL_B2_PATH_ID,
            'title': title,
            'description': f'Upper-intermediate Tamil lesson covering {title.lower()} with specialized vocabulary and complex grammar',
            'lesson_type': 'comprehensive',
            'difficulty_level': 4,  # B2 level
            'estimated_duration': 75,
            'sequence_order': sequence,
            'learning_objectives': [
                f'Master specialized vocabulary in {title.lower()}',
                f'Engage in sophisticated discussions about {title.lower()}',
                f'Apply complex grammar structures in {title.lower()} context',
                f'Critically analyze {title.lower()} concepts and theories'
            ],
            'vocabulary_focus': None,
            'grammar_concepts': [],
            'cultural_notes': '',
            'prerequisite_lessons': [],
            'content_metadata': {
                'title': title,
                'description': f'Upper-intermediate Tamil lesson covering {title.lower()}',
                'vocabulary': vocabulary,
                'conversations': [],
                'grammar_points': [],
                'exercises': [],
                'estimated_duration': 75,
                'learning_objectives': [
                    f'Master specialized vocabulary in {title.lower()}',
                    f'Engage in sophisticated discussions about {title.lower()}',
                    f'Apply complex grammar structures in {title.lower()} context',
                    f'Critically analyze {title.lower()} concepts and theories'
                ]
            },
            'is_active': True,
            'audio_url': None,
            'audio_metadata': {},
            'has_audio': False
        }
        
        try:
            response = requests.post(f"{SUPABASE_URL}/rest/v1/lessons", json=lesson_data, headers=self.headers)
            
            if response.status_code == 201:
                print(f"✅ Created B2 lesson: {title}")
                print(f"📊 Generated: {len(vocabulary)} vocabulary items")
                return True
            else:
                print(f"❌ Database insert failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Error creating lesson: {e}")
            return False
    
    def generate_b2_vocabulary(self, lesson_title: str) -> List[Dict[str, Any]]:
        """Generate B2 level Tamil vocabulary"""
        print(f"🔄 Generating B2 vocabulary for: {lesson_title}")
        
        prompt = f"""
        Generate exactly 40 authentic Tamil vocabulary items for the B2 upper-intermediate lesson: "{lesson_title}"

        Requirements:
        - B2 upper-intermediate level difficulty
        - Specialized and technical vocabulary
        - Abstract concepts and theoretical terms
        - Professional and academic language
        - Complex idiomatic expressions
        - Use authentic Chennai Tamil
        - Each word must be unique

        Return as valid JSON array with 40 items for upper-intermediate complexity.
        """
        
        try:
            response = gemini_model.generate_content(prompt)
            content = response.text
            
            if "```json" in content:
                content = content.split("```json")[1].split("```")[0]
            elif "```" in content:
                content = content.split("```")[1].split("```")[0]
            
            vocabulary = json.loads(content.strip())
            
            if len(vocabulary) != 40:
                vocabulary = vocabulary[:40] if len(vocabulary) > 40 else vocabulary + vocabulary[:40-len(vocabulary)]
            
            lesson_slug = lesson_title.lower().replace(' ', '_').replace('&', 'and')
            for i, vocab in enumerate(vocabulary, 1):
                vocab['word_audio_url'] = f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/b2/{lesson_slug}/vocab_{i:02d}_word.mp3"
                vocab['example_audio_url'] = f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/b2/{lesson_slug}/vocab_{i:02d}_example.mp3"
            
            print(f"✅ Generated {len(vocabulary)} B2 vocabulary items")
            return vocabulary
            
        except Exception as e:
            print(f"❌ Failed to generate vocabulary: {e}")
            return []
    
    def create_all_b2_lessons(self) -> Dict[str, Any]:
        """Create all 30 B2 lessons with vocabulary"""
        print("🚀 B2 UPPER-INTERMEDIATE VOCABULARY VALIDATOR")
        print("Creating 30 upper-intermediate Tamil lessons")
        print("=" * 60)
        
        results = {'total_lessons': len(B2_LESSONS), 'created': 0, 'failed': []}
        
        for i, lesson_title in enumerate(B2_LESSONS, 1):
            print(f"\n📖 Processing {i}/{len(B2_LESSONS)}: {lesson_title}")
            
            if self.create_b2_lesson_with_vocabulary(lesson_title, i):
                results['created'] += 1
            else:
                results['failed'].append(lesson_title)
            
            time.sleep(2)
        
        return results

def main():
    validator = B2VocabularyValidator()
    results = validator.create_all_b2_lessons()
    
    print(f"\n📊 B2 RESULTS: Created {results['created']}/{results['total_lessons']} lessons")
    if results['failed']:
        print(f"❌ Failed: {len(results['failed'])} lessons")

if __name__ == "__main__":
    main()
