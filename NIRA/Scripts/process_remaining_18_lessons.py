#!/usr/bin/env python3
"""
Process Remaining 18 Lessons with Complete Content
Add all remaining lesson types to the main script and process them
"""

import subprocess
import time

# Remaining 18 lessons that need complete content
REMAINING_LESSONS = [
    {"id": "0260c606-e730-455f-8256-01bb5c91118b", "title": "Daily Routines and Activities", "slug": "daily_routines_and_activities", "type": "daily"},
    {"id": "91c89641-a0d8-4490-ba53-7c1e0a1d9dc6", "title": "Shopping and Money", "slug": "shopping_and_money", "type": "shopping"},
    {"id": "3318f37a-adef-4dd2-9f7a-6f8a2618cd38", "title": "Directions and Locations", "slug": "directions_and_locations", "type": "directions"},
    {"id": "eb3367ba-1237-4f35-8553-1c406e2964be", "title": "Health and Body", "slug": "health_and_body", "type": "health"},
    {"id": "40f2ec61-22a9-4408-921f-0624ba7b4857", "title": "Hobbies and Interests", "slug": "hobbies_and_interests", "type": "hobbies"},
    {"id": "07e210c9-4ab6-44c8-a5a1-3cd2ba347a27", "title": "Work and Professions", "slug": "work_and_professions", "type": "work"},
    {"id": "24aa782a-f87e-4249-adb9-4a20313a048f", "title": "Education and School", "slug": "education_and_school", "type": "education"},
    {"id": "9c73ba8d-efcb-4be1-b95e-04efef538f9a", "title": "Technology and Communication", "slug": "technology_and_communication", "type": "technology"},
    {"id": "e2380387-21e9-4635-9fcc-6336063b5853", "title": "Emotions and Feelings", "slug": "emotions_and_feelings", "type": "emotions"},
    {"id": "ac95108b-9214-4f09-be35-6ef513006df5", "title": "Festivals and Celebrations", "slug": "festivals_and_celebrations", "type": "festivals"},
    {"id": "611adab0-aa10-4a95-9485-192f9a74811f", "title": "Vegetables and Healthy Eating", "slug": "vegetables_and_healthy_eating", "type": "vegetables"},
    {"id": "b8f17c93-288c-4ff6-b2b3-40d508dd09e2", "title": "Days, Weeks, Months, and Time", "slug": "days_weeks_months_and_time", "type": "time"},
    {"id": "2c64df41-2054-48fd-8d51-ab6351003ace", "title": "Local Transportation", "slug": "local_transportation", "type": "transport"},
    {"id": "52d43136-cabc-41e1-b054-97c4b0a12d66", "title": "Travel and Long Distance", "slug": "travel_and_long_distance", "type": "travel"},
    {"id": "1dac5f14-bd83-4026-b038-7fc2440a5be1", "title": "Music and Movies", "slug": "music_and_movies", "type": "entertainment"},
    {"id": "5f4e2e08-ef80-41ee-bdd0-66d38a3c43b1", "title": "Famous Landmarks", "slug": "famous_landmarks", "type": "landmarks"},
    {"id": "715b626a-b665-4cf5-b367-712373f76723", "title": "Sports and Games", "slug": "sports_and_games", "type": "sports"}
]

def process_lesson(lesson):
    """Process a single lesson"""
    lesson_id = lesson['id']
    lesson_title = lesson['title']
    lesson_slug = lesson['slug']
    lesson_type = lesson['type']
    
    print(f"\n{'='*70}")
    print(f"🔧 PROCESSING: {lesson_title}")
    print(f"ID: {lesson_id}")
    print(f"Type: {lesson_type}")
    print(f"Slug: {lesson_slug}")
    print(f"{'='*70}")
    
    try:
        result = subprocess.run([
            'python3', 'NIRA/Scripts/fix_all_lessons_complete.py', 
            lesson_id, lesson_title, lesson_slug
        ], capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            if "✅ COMPLETE: Matches Animals & Nature format exactly!" in result.stdout:
                print(f"✅ COMPLETE: {lesson_title}")
                print(f"   📚 25 vocabulary + 15 conversations + 10 grammar + 24 exercises")
                print(f"   🎯 All content is authentic Tamil")
                return "complete"
            elif "✅ SUCCESS:" in result.stdout:
                print(f"⚠️ PARTIAL: {lesson_title}")
                print(f"   📚 Has vocabulary but missing conversations/grammar/exercises")
                print(f"   🔧 Needs specific content implementation")
                return "partial"
            else:
                print(f"❌ FAILED: {lesson_title}")
                return "failed"
        else:
            print(f"❌ FAILED: {lesson_title}")
            print(f"   Error: {result.stderr}")
            return "failed"
            
    except subprocess.TimeoutExpired:
        print(f"⏰ TIMEOUT: {lesson_title}")
        return "failed"
    except Exception as e:
        print(f"❌ ERROR: {lesson_title} - {e}")
        return "failed"

def main():
    """Process all remaining 18 lessons"""
    print("🎯 PROCESS REMAINING 18 LESSONS")
    print("Complete all lessons to match Animals & Nature format")
    print("=" * 70)
    
    complete = 0
    partial = 0
    failed = 0
    
    print(f"📚 Processing {len(REMAINING_LESSONS)} remaining lessons")
    print(f"🎯 Current status: 12 complete, 18 remaining")
    
    for i, lesson in enumerate(REMAINING_LESSONS, 1):
        print(f"\n📋 Progress: {i}/{len(REMAINING_LESSONS)}")
        
        result = process_lesson(lesson)
        
        if result == "complete":
            complete += 1
        elif result == "partial":
            partial += 1
        else:
            failed += 1
        
        # Small delay between lessons
        time.sleep(2)
    
    print(f"\n🎉 PROCESSING SUMMARY:")
    print(f"=" * 50)
    print(f"✅ Complete lessons: {complete}")
    print(f"⚠️ Partial lessons: {partial}")
    print(f"❌ Failed lessons: {failed}")
    print(f"📊 Total processed: {complete + partial + failed}")
    
    total_complete = 12 + complete  # 12 already complete + new complete
    
    print(f"\n📊 OVERALL STATUS:")
    print(f"✅ Total complete lessons: {total_complete}/30")
    print(f"⚠️ Partial lessons needing work: {partial}")
    print(f"❌ Failed lessons: {failed}")
    
    if total_complete >= 25:
        print(f"\n🎊 EXCELLENT PROGRESS!")
        print(f"🌟 {total_complete} lessons have complete authentic Tamil content!")
        print(f"🎯 Ready for audio generation!")
        
        if partial > 0:
            print(f"\n⚠️ {partial} lessons need specific content implementation")
            print(f"These have vocabulary but need conversations/grammar/exercises")
        
    else:
        print(f"\n⚠️ Need to implement specific content for partial lessons")
        print(f"Each lesson type needs its own authentic Tamil content")

    print(f"\n🌍 NEXT STEPS:")
    print(f"1. Add specific content for any partial lessons")
    print(f"2. Generate audio files for all complete lessons")
    print(f"3. Test in iOS app to ensure functionality")
    print(f"4. Verify all content is authentic Tamil like Animals & Nature")

if __name__ == "__main__":
    main()
