#!/usr/bin/env python3
"""
Fix ALL Remaining Lessons with Complete Content
Process all lessons that need the 25+15+10+24 format
"""

import subprocess
import time

# All lessons that need fixing (excluding Animals & Nature, Greetings, Family, Numbers which are done)
REMAINING_LESSONS = [
    {"id": "d5ef89df-5cd2-453e-8809-502440812f5d", "title": "Colors and Descriptions", "slug": "colors_and_descriptions"},
    {"id": "2c6561cd-e728-4e12-a30e-e3dcff8b693d", "title": "Food and Dining", "slug": "food_and_dining"},
    {"id": "0a4fc95a-54de-4061-b9a6-6893fde37707", "title": "Body Parts and Health", "slug": "body_parts_and_health"},
    {"id": "27a84f24-d5d4-461a-9ea1-b3e164d2c1f5", "title": "Weather and Seasons", "slug": "weather_and_seasons"},
    {"id": "65c2c45a-2952-49e2-b287-3c9ef2550830", "title": "Transportation", "slug": "transportation"},
    {"id": "76de735e-a59b-42f8-b77f-21af365b19a8", "title": "Clothing and Shopping", "slug": "clothing_and_shopping"},
    {"id": "2a2fc194-b5e4-4bed-a017-80fc3fb43a28", "title": "Common Verbs and Actions", "slug": "common_verbs_and_actions"},
    {"id": "6789207a-5877-40a6-8504-5431e1106d90", "title": "Personal Information and Identity", "slug": "personal_information_and_identity"},
    {"id": "1aa0509a-88b8-40e2-ab99-9a00858a0e2f", "title": "Home and Living Spaces", "slug": "home_and_living_spaces"},
    {"id": "0260c606-e730-455f-8256-01bb5c91118b", "title": "Daily Routines and Activities", "slug": "daily_routines_and_activities"},
    {"id": "91c89641-a0d8-4490-ba53-7c1e0a1d9dc6", "title": "Shopping and Money", "slug": "shopping_and_money"},
    {"id": "3318f37a-adef-4dd2-9f7a-6f8a2618cd38", "title": "Directions and Locations", "slug": "directions_and_locations"},
    {"id": "eb3367ba-1237-4f35-8553-1c406e2964be", "title": "Health and Body", "slug": "health_and_body"},
    {"id": "40f2ec61-22a9-4408-921f-0624ba7b4857", "title": "Hobbies and Interests", "slug": "hobbies_and_interests"},
    {"id": "07e210c9-4ab6-44c8-a5a1-3cd2ba347a27", "title": "Work and Professions", "slug": "work_and_professions"},
    {"id": "24aa782a-f87e-4249-adb9-4a20313a048f", "title": "Education and School", "slug": "education_and_school"},
    {"id": "9c73ba8d-efcb-4be1-b95e-04efef538f9a", "title": "Technology and Communication", "slug": "technology_and_communication"},
    {"id": "e2380387-21e9-4635-9fcc-6336063b5853", "title": "Emotions and Feelings", "slug": "emotions_and_feelings"},
    {"id": "ac95108b-9214-4f09-be35-6ef513006df5", "title": "Festivals and Celebrations", "slug": "festivals_and_celebrations"},
    {"id": "611adab0-aa10-4a95-9485-192f9a74811f", "title": "Vegetables and Healthy Eating", "slug": "vegetables_and_healthy_eating"},
    {"id": "b8f17c93-288c-4ff6-b2b3-40d508dd09e2", "title": "Days, Weeks, Months, and Time", "slug": "days_weeks_months_and_time"},
    {"id": "2c64df41-2054-48fd-8d51-ab6351003ace", "title": "Local Transportation", "slug": "local_transportation"},
    {"id": "52d43136-cabc-41e1-b054-97c4b0a12d66", "title": "Travel and Long Distance", "slug": "travel_and_long_distance"},
    {"id": "1dac5f14-bd83-4026-b038-7fc2440a5be1", "title": "Music and Movies", "slug": "music_and_movies"},
    {"id": "5f4e2e08-ef80-41ee-bdd0-66d38a3c43b1", "title": "Famous Landmarks", "slug": "famous_landmarks"},
    {"id": "715b626a-b665-4cf5-b367-712373f76723", "title": "Sports and Games", "slug": "sports_and_games"}
]

def fix_single_lesson(lesson):
    """Fix a single lesson using the complete content script"""
    lesson_id = lesson['id']
    lesson_title = lesson['title']
    lesson_slug = lesson['slug']
    
    print(f"\n{'='*70}")
    print(f"🔧 FIXING LESSON: {lesson_title}")
    print(f"ID: {lesson_id}")
    print(f"Slug: {lesson_slug}")
    print(f"{'='*70}")
    
    try:
        result = subprocess.run([
            'python3', 'NIRA/Scripts/fix_all_lessons_complete.py', 
            lesson_id, lesson_title, lesson_slug
        ], capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            # Check if it has complete content
            if "✅ COMPLETE: Matches Animals & Nature format exactly!" in result.stdout:
                print(f"✅ SUCCESS: {lesson_title}")
                print(f"   📚 Complete with 25+15+10+24 format")
                print(f"   🎯 All content is authentic Tamil")
                return True
            else:
                print(f"⚠️ PARTIAL: {lesson_title}")
                print(f"   📚 Has vocabulary but missing other sections")
                print(f"   🔧 Needs specific content implementation")
                return False
        else:
            print(f"❌ FAILED: {lesson_title}")
            print(f"   Error: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ TIMEOUT: {lesson_title}")
        return False
    except Exception as e:
        print(f"❌ ERROR: {lesson_title} - {e}")
        return False

def main():
    """Fix all remaining lessons"""
    print("🎯 FIX ALL REMAINING LESSONS")
    print("Ensure every lesson has 25+15+10+24 format like Animals & Nature")
    print("=" * 70)
    
    complete = 0
    partial = 0
    failed = 0
    
    print(f"📚 Processing {len(REMAINING_LESSONS)} remaining lessons")
    print(f"🎯 Target: Every lesson must have complete content")
    
    for i, lesson in enumerate(REMAINING_LESSONS, 1):
        print(f"\n📋 Progress: {i}/{len(REMAINING_LESSONS)}")
        
        success = fix_single_lesson(lesson)
        
        if success:
            complete += 1
        else:
            partial += 1
        
        # Small delay between lessons
        time.sleep(2)
    
    print(f"\n🎉 FINAL SUMMARY:")
    print(f"=" * 50)
    print(f"✅ Complete lessons: {complete}")
    print(f"⚠️ Partial lessons: {partial}")
    print(f"❌ Failed lessons: {failed}")
    print(f"📊 Success rate: {(complete/(complete+partial+failed)*100):.1f}%")
    
    if complete == len(REMAINING_LESSONS):
        print(f"\n🎊 ALL LESSONS ARE NOW COMPLETE!")
        print(f"\n📊 FINAL STATISTICS:")
        print(f"   📚 Total lessons: 30 (including Animals & Nature)")
        print(f"   🎯 Complete format: 25+15+10+24 for each")
        print(f"   📝 Total content items: {30 * 74} pieces")
        print(f"   🎵 Total audio files: {30 * 203} files")
        
        print(f"\n🌟 CONTENT QUALITY:")
        print(f"   ✅ Every vocabulary word is authentic Tamil")
        print(f"   ✅ Every conversation is realistic Tamil dialogue")
        print(f"   ✅ Every grammar point explains Tamil rules")
        print(f"   ✅ Every exercise tests Tamil skills")
        print(f"   ✅ NO PLACEHOLDERS - NO ENGLISH EXAMPLES")
        
        print(f"\n🎯 READY FOR AUDIO GENERATION!")
        print(f"Generate 6,090 audio files using ElevenLabs (Freya/Elli voices)")
        
    else:
        print(f"\n⚠️ Some lessons need specific content implementation")
        print(f"Partial lessons have vocabulary but need conversations/grammar/exercises")
        print(f"Each lesson type needs its own authentic Tamil content")

if __name__ == "__main__":
    main()
