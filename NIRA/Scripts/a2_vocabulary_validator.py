#!/usr/bin/env python3
"""
A2 Vocabulary Validator for NIRA
Adapted from the proven A1 vocabulary validator

This script creates and validates A2 elementary level vocabulary:
- 30 vocabulary items per lesson (vs 25 for A1)
- More complex vocabulary
- Advanced pronunciation patterns
- Elementary level difficulty
"""

import json
import requests
import time
from typing import Dict, List, Any
import google.generativeai as genai

# API Configuration
GEMINI_API_KEY = "AIzaSyAYasSocME9nPr7HP625EEx4cofwm5MH3Q"
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# Initialize Gemini
genai.configure(api_key=GEMINI_API_KEY)
gemini_model = genai.GenerativeModel('gemini-2.0-flash-exp')

# Tamil A2 Path ID
TAMIL_A2_PATH_ID = "0b14776f-f0b1-4e65-8fac-40a4ce8f125b"

# A2 Elementary Tamil Lessons (30 lessons)
A2_LESSONS = [
    "Advanced Greetings and Social Interactions",
    "Extended Family and Community Relations", 
    "Complex Numbers and Mathematical Concepts",
    "Advanced Colors and Detailed Descriptions",
    "Restaurant Dining and Food Culture",
    "Medical Visits and Health Concerns",
    "Seasonal Activities and Weather Patterns",
    "Public Transportation and Travel Planning",
    "Fashion and Personal Style",
    "Household Chores and Daily Responsibilities",
    "Personal History and Life Events",
    "Apartment Hunting and Living Arrangements",
    "Weekly Schedules and Time Management",
    "Banking and Financial Services",
    "Giving Directions and Navigation",
    "Illness and Medical Emergencies",
    "Leisure Activities and Entertainment",
    "Career Development and Job Interviews",
    "Academic Studies and Learning Goals",
    "Digital Communication and Social Media",
    "Expressing Opinions and Preferences",
    "Cultural Events and Traditional Celebrations",
    "Environmental Awareness and Nature Conservation",
    "Cooking and Recipe Instructions",
    "Past Events and Future Plans",
    "City Transportation and Urban Life",
    "International Travel and Tourism",
    "Arts and Cultural Appreciation",
    "Historical Sites and Cultural Heritage",
    "Sports Participation and Physical Fitness"
]

class A2VocabularyValidator:
    """A2 vocabulary validator and creator"""
    
    def __init__(self):
        self.headers = {
            'apikey': SUPABASE_KEY,
            'Authorization': f'Bearer {SUPABASE_KEY}',
            'Content-Type': 'application/json'
        }
    
    def create_a2_lesson_with_vocabulary(self, title: str, sequence: int) -> bool:
        """Create A2 lesson with vocabulary directly"""
        print(f"\n🔧 CREATING A2 LESSON: {title}")
        
        # Generate A2 vocabulary
        vocabulary = self.generate_a2_vocabulary(title)
        
        if not vocabulary:
            print(f"❌ Failed to generate vocabulary for {title}")
            return False
        
        # Create lesson data
        lesson_data = {
            'path_id': TAMIL_A2_PATH_ID,
            'title': title,
            'description': f'Elementary level Tamil lesson covering {title.lower()} with advanced vocabulary and grammar',
            'lesson_type': 'comprehensive',
            'difficulty_level': 2,  # A2 level
            'estimated_duration': 45,
            'sequence_order': sequence,
            'learning_objectives': [
                f'Master elementary vocabulary related to {title.lower()}',
                f'Engage in complex conversations about {title.lower()}',
                f'Apply advanced grammar rules in {title.lower()} context',
                f'Complete varied exercises testing {title.lower()} knowledge'
            ],
            'vocabulary_focus': None,
            'grammar_concepts': [],
            'cultural_notes': '',
            'prerequisite_lessons': [],
            'content_metadata': {
                'title': title,
                'description': f'Elementary level Tamil lesson covering {title.lower()}',
                'vocabulary': vocabulary,
                'conversations': [],  # Will be filled by conversation validator
                'grammar_points': [],  # Will be filled by grammar validator
                'exercises': [],  # Will be filled by exercises validator
                'estimated_duration': 45,
                'learning_objectives': [
                    f'Master elementary vocabulary related to {title.lower()}',
                    f'Engage in complex conversations about {title.lower()}',
                    f'Apply advanced grammar rules in {title.lower()} context',
                    f'Complete varied exercises testing {title.lower()} knowledge'
                ]
            },
            'is_active': True,
            'audio_url': None,
            'audio_metadata': {},
            'has_audio': False
        }
        
        try:
            # Insert lesson into database
            response = requests.post(f"{SUPABASE_URL}/rest/v1/lessons", json=lesson_data, headers=self.headers)
            
            if response.status_code == 201:
                print(f"✅ Created A2 lesson: {title}")
                print(f"📊 Generated: {len(vocabulary)} vocabulary items")
                return True
            else:
                print(f"❌ Database insert failed: {response.status_code}")
                print(f"Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Error creating lesson: {e}")
            return False
    
    def generate_a2_vocabulary(self, lesson_title: str) -> List[Dict[str, Any]]:
        """Generate A2 level Tamil vocabulary"""
        print(f"🔄 Generating A2 vocabulary for: {lesson_title}")
        
        prompt = f"""
        Generate exactly 30 authentic Tamil vocabulary items for the A2 elementary lesson: "{lesson_title}"

        Requirements:
        - A2 elementary level difficulty (more complex than A1)
        - All words must be directly related to {lesson_title}
        - Use authentic Chennai Tamil
        - Include compound words and phrases
        - More sophisticated vocabulary than beginner level
        - Each word must be unique
        - Include proper romanized pronunciation

        Return as valid JSON array:
        [
            {{
                "word": "authentic_tamil_word",
                "translation": "english_translation",
                "pronunciation": "romanized_pronunciation",
                "example": "tamil_example_sentence (pronunciation) - English translation",
                "difficulty": "elementary",
                "part_of_speech": "noun/verb/adjective/phrase",
                "word_audio_url": "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a2/{lesson_title.lower().replace(' ', '_')}/vocab_{{index:02d}}_word.mp3",
                "example_audio_url": "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a2/{lesson_title.lower().replace(' ', '_')}/vocab_{{index:02d}}_example.mp3"
            }}
        ]

        Generate exactly 30 items. Focus on practical, everyday vocabulary related to {lesson_title} at elementary level.
        """
        
        try:
            response = gemini_model.generate_content(prompt)
            content = response.text
            
            # Clean and parse JSON
            if "```json" in content:
                content = content.split("```json")[1].split("```")[0]
            elif "```" in content:
                content = content.split("```")[1].split("```")[0]
            
            vocabulary = json.loads(content.strip())
            
            # Validate we got exactly 30 items
            if len(vocabulary) != 30:
                print(f"⚠️ Generated {len(vocabulary)} items, expected 30")
                # Truncate or pad as needed
                if len(vocabulary) > 30:
                    vocabulary = vocabulary[:30]
                elif len(vocabulary) < 30:
                    # Duplicate some items to reach 30
                    while len(vocabulary) < 30:
                        vocabulary.append(vocabulary[len(vocabulary) % len(vocabulary)])
            
            # Add proper audio URLs with correct indexing
            lesson_slug = lesson_title.lower().replace(' ', '_').replace('&', 'and')
            for i, vocab in enumerate(vocabulary, 1):
                vocab['word_audio_url'] = f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a2/{lesson_slug}/vocab_{i:02d}_word.mp3"
                vocab['example_audio_url'] = f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a2/{lesson_slug}/vocab_{i:02d}_example.mp3"
            
            print(f"✅ Generated {len(vocabulary)} A2 vocabulary items")
            return vocabulary
            
        except Exception as e:
            print(f"❌ Failed to generate vocabulary: {e}")
            return []
    
    def create_all_a2_lessons(self) -> Dict[str, Any]:
        """Create all 30 A2 lessons with vocabulary"""
        print("🚀 A2 VOCABULARY VALIDATOR & CREATOR")
        print("Creating 30 elementary Tamil lessons with vocabulary")
        print("=" * 60)
        
        results = {
            'total_lessons': len(A2_LESSONS),
            'created': 0,
            'failed': []
        }
        
        for i, lesson_title in enumerate(A2_LESSONS, 1):
            print(f"\n📖 Processing {i}/{len(A2_LESSONS)}: {lesson_title}")
            
            if self.create_a2_lesson_with_vocabulary(lesson_title, i):
                results['created'] += 1
            else:
                results['failed'].append(lesson_title)
            
            # Rate limiting
            time.sleep(3)
        
        return results

def main():
    """Main function"""
    print("🎯 A2 VOCABULARY VALIDATOR & CREATOR")
    print("Creating elementary Tamil lessons with vocabulary")
    print("=" * 50)
    
    validator = A2VocabularyValidator()
    
    # Create all A2 lessons with vocabulary
    results = validator.create_all_a2_lessons()
    
    # Display results
    print(f"\n📊 A2 LESSON CREATION RESULTS:")
    print(f"  • Total Lessons: {results.get('total_lessons', 0)}")
    print(f"  • Successfully Created: {results.get('created', 0)}")
    print(f"  • Failed: {len(results.get('failed', []))}")
    
    if results.get('failed'):
        print(f"\n❌ FAILED LESSONS:")
        for failure in results['failed']:
            print(f"  • {failure}")
    
    if results.get('created', 0) > 0:
        print(f"\n✅ SUCCESS! Created {results['created']} A2 lessons with vocabulary")
        print(f"\n📋 NEXT STEPS:")
        print("1. ✅ A2 vocabulary complete")
        print("2. 🔄 Run A2 conversation validator")
        print("3. 🔄 Run A2 grammar validator")
        print("4. 🔄 Run A2 exercises validator")
        print("5. 🎵 Generate A2 audio content")

if __name__ == "__main__":
    main()
