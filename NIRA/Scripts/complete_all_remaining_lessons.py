#!/usr/bin/env python3
"""
Complete All Remaining Tamil A1 Lessons
Process all lessons one by one using the proven complete_one_lesson.py script
"""

import subprocess
import time
import requests

# Configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"
TAMIL_A1_PATH_ID = "6b427613-420f-4586-bce8-2773d722f0b4"

# All Tamil A1 lessons (from the database query)
ALL_LESSONS = [
    {"id": "342230c2-8ea9-495d-bbef-ab0bec4df7be", "title": "Basic Greetings and Introductions", "sequence_order": 1},
    {"id": "11293be2-3ffc-4002-9e0a-74c36ae8685f", "title": "Family Members and Relationships", "sequence_order": 2},
    {"id": "5e850209-684f-4c01-a886-78b38e32a154", "title": "Numbers and Counting", "sequence_order": 3},
    {"id": "d5ef89df-5cd2-453e-8809-502440812f5d", "title": "Colors and Descriptions", "sequence_order": 5},
    {"id": "2c6561cd-e728-4e12-a30e-e3dcff8b693d", "title": "Food and Dining", "sequence_order": 6},
    {"id": "0a4fc95a-54de-4061-b9a6-6893fde37707", "title": "Body Parts and Health", "sequence_order": 7},
    {"id": "27a84f24-d5d4-461a-9ea1-b3e164d2c1f5", "title": "Weather and Seasons", "sequence_order": 8},
    {"id": "65c2c45a-2952-49e2-b287-3c9ef2550830", "title": "Transportation", "sequence_order": 9},
    {"id": "76de735e-a59b-42f8-b77f-21af365b19a8", "title": "Clothing and Shopping", "sequence_order": 10},
    {"id": "2a2fc194-b5e4-4bed-a017-80fc3fb43a28", "title": "Common Verbs and Actions", "sequence_order": 11},
    {"id": "6789207a-5877-40a6-8504-5431e1106d90", "title": "Personal Information and Identity", "sequence_order": 12},
    {"id": "1aa0509a-88b8-40e2-ab99-9a00858a0e2f", "title": "Home and Living Spaces", "sequence_order": 13},
    {"id": "0260c606-e730-455f-8256-01bb5c91118b", "title": "Daily Routines and Activities", "sequence_order": 14},
    {"id": "91c89641-a0d8-4490-ba53-7c1e0a1d9dc6", "title": "Shopping and Money", "sequence_order": 15},
    {"id": "3318f37a-adef-4dd2-9f7a-6f8a2618cd38", "title": "Directions and Locations", "sequence_order": 16},
    {"id": "eb3367ba-1237-4f35-8553-1c406e2964be", "title": "Health and Body", "sequence_order": 17},
    {"id": "40f2ec61-22a9-4408-921f-0624ba7b4857", "title": "Hobbies and Interests", "sequence_order": 18},
    {"id": "07e210c9-4ab6-44c8-a5a1-3cd2ba347a27", "title": "Work and Professions", "sequence_order": 19},
    {"id": "24aa782a-f87e-4249-adb9-4a20313a048f", "title": "Education and School", "sequence_order": 20},
    {"id": "9c73ba8d-efcb-4be1-b95e-04efef538f9a", "title": "Technology and Communication", "sequence_order": 21},
    {"id": "e2380387-21e9-4635-9fcc-6336063b5853", "title": "Emotions and Feelings", "sequence_order": 22},
    {"id": "ac95108b-9214-4f09-be35-6ef513006df5", "title": "Festivals and Celebrations", "sequence_order": 23},
    {"id": "b966c742-d36d-4d94-9e35-7c17a5039487", "title": "Animals and Nature", "sequence_order": 24},  # Already complete
    {"id": "611adab0-aa10-4a95-9485-192f9a74811f", "title": "Vegetables and Healthy Eating", "sequence_order": 26},
    {"id": "b8f17c93-288c-4ff6-b2b3-40d508dd09e2", "title": "Days, Weeks, Months, and Time", "sequence_order": 27},
    {"id": "2c64df41-2054-48fd-8d51-ab6351003ace", "title": "Local Transportation", "sequence_order": 28},
    {"id": "52d43136-cabc-41e1-b054-97c4b0a12d66", "title": "Travel and Long Distance", "sequence_order": 29},
    {"id": "1dac5f14-bd83-4026-b038-7fc2440a5be1", "title": "Music and Movies", "sequence_order": 30},
    {"id": "5f4e2e08-ef80-41ee-bdd0-66d38a3c43b1", "title": "Famous Landmarks", "sequence_order": 31},
    {"id": "715b626a-b665-4cf5-b367-712373f76723", "title": "Sports and Games", "sequence_order": 32}
]

def check_lesson_completion(lesson_id):
    """Check if a lesson is already complete"""
    headers = {
        'apikey': SUPABASE_KEY,
        'Authorization': f'Bearer {SUPABASE_KEY}'
    }
    
    params = {
        'select': 'has_audio,audio_metadata',
        'id': f'eq.{lesson_id}'
    }
    
    try:
        response = requests.get(f"{SUPABASE_URL}/rest/v1/lessons", headers=headers, params=params)
        if response.status_code == 200:
            lessons = response.json()
            if lessons:
                lesson = lessons[0]
                has_audio = lesson.get('has_audio', False)
                audio_metadata = lesson.get('audio_metadata', {})
                audio_count = audio_metadata.get('generated_audio_count', 0)
                return has_audio and audio_count >= 200  # Consider complete if has 200+ audio files
        return False
    except Exception as e:
        print(f"Error checking lesson {lesson_id}: {e}")
        return False

def complete_single_lesson(lesson):
    """Complete a single lesson using the proven script"""
    lesson_id = lesson['id']
    lesson_title = lesson['title']
    sequence_order = lesson['sequence_order']
    
    print(f"\n{'='*70}")
    print(f"🚀 PROCESSING LESSON {sequence_order}: {lesson_title}")
    print(f"ID: {lesson_id}")
    print(f"{'='*70}")
    
    # Check if already complete
    if check_lesson_completion(lesson_id):
        print(f"✅ ALREADY COMPLETE: {lesson_title}")
        return True
    
    # Run the complete_one_lesson.py script
    try:
        result = subprocess.run([
            'python3', 'NIRA/Scripts/complete_one_lesson.py', lesson_id
        ], capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print(f"✅ SUCCESS: {lesson_title}")
            print(f"   📊 Content structure created")
            print(f"   💾 Database updated")
            print(f"   🎵 203 audio URLs generated")
            return True
        else:
            print(f"❌ FAILED: {lesson_title}")
            print(f"   Error: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ TIMEOUT: {lesson_title}")
        return False
    except Exception as e:
        print(f"❌ ERROR: {lesson_title} - {e}")
        return False

def main():
    """Complete all remaining Tamil A1 lessons"""
    print("🎯 COMPLETE ALL REMAINING TAMIL A1 LESSONS")
    print("Using proven complete_one_lesson.py script")
    print("=" * 70)
    
    completed = 0
    failed = 0
    skipped = 0
    
    # Process lessons in sequence order
    sorted_lessons = sorted(ALL_LESSONS, key=lambda x: x['sequence_order'])
    
    print(f"📚 Found {len(sorted_lessons)} Tamil A1 lessons to process")
    
    for i, lesson in enumerate(sorted_lessons, 1):
        print(f"\n📋 Progress: {i}/{len(sorted_lessons)}")
        
        # Skip Animals and Nature (already complete)
        if lesson['title'] == "Animals and Nature":
            print(f"⏭️ SKIPPING: {lesson['title']} (reference lesson - already complete)")
            skipped += 1
            continue
        
        success = complete_single_lesson(lesson)
        
        if success:
            completed += 1
        else:
            failed += 1
        
        # Small delay between lessons to avoid overwhelming the API
        time.sleep(2)
    
    print(f"\n🎉 COMPLETION SUMMARY:")
    print(f"=" * 50)
    print(f"✅ Successfully completed: {completed} lessons")
    print(f"⏭️ Skipped (already complete): {skipped} lessons")
    print(f"❌ Failed: {failed} lessons")
    print(f"📊 Total processed: {completed + skipped + failed} lessons")
    
    if failed == 0:
        print(f"\n🎊 ALL TAMIL A1 LESSONS COMPLETED!")
        print(f"📚 Total lessons: {len(sorted_lessons)}")
        print(f"🎵 Total audio URLs: {(completed + skipped) * 203}")
        print(f"\n🎯 NEXT STEPS:")
        print(f"1. Generate actual audio files using ElevenLabs batch generation")
        print(f"2. Upload all audio files to Supabase storage")
        print(f"3. Test all lessons in iOS app")
        print(f"4. Verify complete functionality like Animals & Nature")
        print(f"\n🌟 Tamil A1 will be 100% complete with:")
        print(f"   📚 {(completed + skipped) * 25} vocabulary items")
        print(f"   💬 {(completed + skipped) * 15} conversations")
        print(f"   📖 {(completed + skipped) * 10} grammar points")
        print(f"   🧩 {(completed + skipped) * 24} exercises")
        print(f"   🎵 {(completed + skipped) * 203} audio files")
    else:
        print(f"\n⚠️ Some lessons failed. Review and retry failed lessons.")
        print(f"All successful lessons are ready for audio generation.")

if __name__ == "__main__":
    main()
