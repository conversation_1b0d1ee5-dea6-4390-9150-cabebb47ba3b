#!/usr/bin/env python3
"""
Add proper unique exercises to Body Parts lesson
"""

import requests
import json

def add_exercises():
    # Get current lesson
    url = "https://lyaojebttnqilmdosmjk.supabase.co/rest/v1/lessons?title=eq.Body%20Parts%20and%20Health&select=id,content_metadata"
    headers = {
        "apikey": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c",
        "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"
    }
    
    response = requests.get(url, headers=headers)
    data = response.json()[0]
    lesson_id = data['id']
    content = data['content_metadata']
    
    # Create 12 unique exercises
    exercises = [
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "What is the Tamil word for 'head'?",
            "options": ["தலை", "கண்", "காது", "மூக்கு"],
            "options_pronunciations": ["thalai", "kan", "kaadhu", "mookku"],
            "correctAnswer": 0,
            "explanation": "தலை (thalai) means head in Tamil"
        },
        {
            "type": "multiple_choice", 
            "points": 10,
            "question": "How do you say 'My hand hurts' in Tamil?",
            "options": ["என் கை வலிக்கிறது", "என் கால் வலிக்கிறது", "என் தலை வலிக்கிறது", "என் வயிறு வலிக்கிறது"],
            "options_pronunciations": ["en kai valikkiradhu", "en kaal valikkiradhu", "en thalai valikkiradhu", "en vayiru valikkiradhu"],
            "correctAnswer": 0,
            "explanation": "என் கை வலிக்கிறது (en kai valikkiradhu) means my hand hurts"
        },
        {
            "type": "multiple_choice",
            "points": 10, 
            "question": "What does 'கண்' mean?",
            "options": ["Eye", "Ear", "Nose", "Mouth"],
            "options_pronunciations": ["eye", "ear", "nose", "mouth"],
            "correctAnswer": 0,
            "explanation": "கண் (kan) means eye in Tamil"
        },
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "What is the Tamil word for 'doctor'?",
            "options": ["மருத்துவர்", "மருந்து", "மருத்துவமனை", "நோய்"],
            "options_pronunciations": ["maruththuvar", "marundhu", "maruththuvamani", "noy"],
            "correctAnswer": 0,
            "explanation": "மருத்துவர் (maruththuvar) means doctor in Tamil"
        },
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "How do you say 'I have fever' in Tamil?",
            "options": ["எனக்கு காய்ச்சல் இருக்கிறது", "எனக்கு தலைவலி இருக்கிறது", "எனக்கு இருமல் இருக்கிறது", "எனக்கு சளி இருக்கிறது"],
            "options_pronunciations": ["enakku kaaychhal irukkiRadhu", "enakku thalai vali irukkiRadhu", "enakku irumal irukkiRadhu", "enakku sali irukkiRadhu"],
            "correctAnswer": 0,
            "explanation": "எனக்கு காய்ச்சல் இருக்கிறது (enakku kaaychhal irukkiRadhu) means I have fever"
        },
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "What does 'வலி' mean?",
            "options": ["Pain", "Medicine", "Health", "Doctor"],
            "options_pronunciations": ["pain", "medicine", "health", "doctor"],
            "correctAnswer": 0,
            "explanation": "வலி (vali) means pain in Tamil"
        },
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "What is the Tamil word for 'stomach'?",
            "options": ["வயிறு", "மார்பு", "முதுகு", "கழுத்து"],
            "options_pronunciations": ["vayiru", "maarbu", "mudhugu", "kazhutthu"],
            "correctAnswer": 0,
            "explanation": "வயிறு (vayiru) means stomach in Tamil"
        },
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "How do you say 'Take medicine' in Tamil?",
            "options": ["மருந்து சாப்பிடுங்கள்", "மருத்துவரை பாருங்கள்", "ஓய்வு எடுங்கள்", "தண்ணீர் குடியுங்கள்"],
            "options_pronunciations": ["marundhu saappidungal", "maruththuvarai paarungal", "oyvu edungal", "thanneer kudiyungal"],
            "correctAnswer": 0,
            "explanation": "மருந்து சாப்பிடுங்கள் (marundhu saappidungal) means take medicine"
        },
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "What does 'காது' mean?",
            "options": ["Ear", "Eye", "Nose", "Mouth"],
            "options_pronunciations": ["ear", "eye", "nose", "mouth"],
            "correctAnswer": 0,
            "explanation": "காது (kaadhu) means ear in Tamil"
        },
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "What is the Tamil word for 'leg'?",
            "options": ["கால்", "கை", "விரல்", "தோள்"],
            "options_pronunciations": ["kaal", "kai", "viral", "thol"],
            "correctAnswer": 0,
            "explanation": "கால் (kaal) means leg in Tamil"
        },
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "How do you ask 'How is your health?' in Tamil?",
            "options": ["உங்கள் உடல்நிலை எப்படி?", "உங்கள் பெயர் என்ன?", "நீங்கள் எங்கே போகிறீர்கள்?", "என்ன வேலை செய்கிறீர்கள்?"],
            "options_pronunciations": ["ungal udalnilai eppaddi?", "ungal peyar enna?", "neengal engE pokiReerkal?", "enna velai seykiReerkal?"],
            "correctAnswer": 0,
            "explanation": "உங்கள் உடல்நிலை எப்படி? (ungal udalnilai eppaddi?) means how is your health?"
        },
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "What does 'ஆரோக்கியம்' mean?",
            "options": ["Health/Wellness", "Disease", "Pain", "Medicine"],
            "options_pronunciations": ["health/wellness", "disease", "pain", "medicine"],
            "correctAnswer": 0,
            "explanation": "ஆரோக்கியம் (aarokkiyam) means health or wellness in Tamil"
        }
    ]
    
    content['exercises'] = exercises
    
    # Update lesson
    update_url = f"https://lyaojebttnqilmdosmjk.supabase.co/rest/v1/lessons?id=eq.{lesson_id}"
    update_headers = {**headers, "Content-Type": "application/json", "Prefer": "return=minimal"}
    
    response = requests.patch(update_url, headers=update_headers, json={"content_metadata": content})
    
    print(f"✅ Added {len(exercises)} unique exercises with pronunciations")
    return response.status_code == 204

if __name__ == "__main__":
    add_exercises()
