#!/usr/bin/env python3
"""
Create A2 Elementary Tamil Lessons for NIRA
Following the proven A1 structure and systematic approach

This script creates 30 A2 elementary level lessons with:
- More complex vocabulary (30 items per lesson)
- Longer conversations (20 per lesson)
- Advanced grammar points (15 per lesson)
- Complex exercises (30 per lesson)
- Building on A1 foundation
"""

import json
import requests
import time
from typing import Dict, List, Any

# Supabase configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# A2 Elementary Tamil Lessons (30 lessons)
A2_LESSONS = [
    # Building on A1 foundation with more complexity
    "Advanced Greetings and Social Interactions",
    "Extended Family and Community Relations", 
    "Complex Numbers and Mathematical Concepts",
    "Advanced Colors and Detailed Descriptions",
    "Restaurant Dining and Food Culture",
    "Medical Visits and Health Concerns",
    "Seasonal Activities and Weather Patterns",
    "Public Transportation and Travel Planning",
    "Fashion and Personal Style",
    "Household Chores and Daily Responsibilities",
    "Personal History and Life Events",
    "Apartment Hunting and Living Arrangements",
    "Weekly Schedules and Time Management",
    "Banking and Financial Services",
    "Giving Directions and Navigation",
    "Illness and Medical Emergencies",
    "Leisure Activities and Entertainment",
    "Career Development and Job Interviews",
    "Academic Studies and Learning Goals",
    "Digital Communication and Social Media",
    "Expressing Opinions and Preferences",
    "Cultural Events and Traditional Celebrations",
    "Environmental Awareness and Nature Conservation",
    "Cooking and Recipe Instructions",
    "Past Events and Future Plans",
    "City Transportation and Urban Life",
    "International Travel and Tourism",
    "Arts and Cultural Appreciation",
    "Historical Sites and Cultural Heritage",
    "Sports Participation and Physical Fitness"
]

class A2LessonCreator:
    """Creates A2 elementary level lessons"""
    
    def __init__(self):
        self.headers = {
            'apikey': SUPABASE_KEY,
            'Authorization': f'Bearer {SUPABASE_KEY}',
            'Content-Type': 'application/json'
        }
    
    def get_or_create_a2_path(self) -> str:
        """Get or create Tamil A2 learning path"""
        print("🔍 Getting Tamil A2 learning path...")

        # Tamil language ID and agent ID
        tamil_language_id = "4df28de4-168f-4d8e-a304-b785e9295644"
        agent_id = "7f458eb9-5b56-4e91-97ea-002ddc3d05b3"

        # First, try to find existing A2 path
        params = {
            'select': 'id',
            'language_id': f'eq.{tamil_language_id}',
            'level': 'eq.A2'
        }

        response = requests.get(f"{SUPABASE_URL}/rest/v1/learning_paths", headers=self.headers, params=params)

        if response.status_code == 200 and response.json():
            path_id = response.json()[0]['id']
            print(f"✅ Found existing Tamil A2 path: {path_id}")
            return path_id

        # Create new A2 path
        print("🔄 Creating new Tamil A2 learning path...")

        path_data = {
            'name': 'Tamil A2 - Elementary',
            'description': 'Elementary level Tamil language learning with complex conversations and grammar',
            'language_id': tamil_language_id,
            'agent_id': agent_id,
            'level': 'A2',
            'estimated_hours': 80,
            'is_active': True
        }

        response = requests.post(f"{SUPABASE_URL}/rest/v1/learning_paths", json=path_data, headers=self.headers)

        if response.status_code == 201:
            path_id = response.json()[0]['id']
            print(f"✅ Created Tamil A2 path: {path_id}")
            return path_id
        else:
            print(f"❌ Failed to create A2 path: {response.status_code}")
            print(f"Response: {response.text}")
            return None
    
    def create_lesson_structure(self, title: str, sequence: int, path_id: str) -> Dict[str, Any]:
        """Create basic lesson structure for A2 level"""
        return {
            'title': title,
            'description': f'Elementary level lesson covering {title.lower()} with advanced vocabulary and grammar',
            'path_id': path_id,
            'sequence_order': sequence,
            'difficulty_level': 'A2',
            'estimated_duration_minutes': 45,
            'is_active': True,
            'has_audio': False,  # Will be set to True after audio generation
            'content_metadata': {
                'vocabulary': [],  # Will be filled by vocabulary validator
                'conversations': [],  # Will be filled by conversation validator
                'grammar_points': [],  # Will be filled by grammar validator
                'exercises': [],  # Will be filled by exercises validator
                'learning_objectives': [
                    f'Master elementary vocabulary related to {title.lower()}',
                    f'Engage in complex conversations about {title.lower()}',
                    f'Apply advanced grammar rules in {title.lower()} context',
                    f'Complete varied exercises testing {title.lower()} knowledge'
                ],
                'prerequisites': ['Tamil A1 - Beginner'],
                'difficulty_indicators': {
                    'vocabulary_complexity': 'elementary',
                    'grammar_complexity': 'elementary', 
                    'conversation_length': 'medium',
                    'exercise_variety': 'high'
                }
            },
            'created_at': 'now()',
            'updated_at': 'now()'
        }
    
    def create_all_a2_lessons(self) -> Dict[str, Any]:
        """Create all 30 A2 lessons"""
        print("🚀 CREATING TAMIL A2 ELEMENTARY LESSONS")
        print("Following proven A1 structure with A2 complexity")
        print("=" * 60)
        
        # Get or create A2 path
        path_id = self.get_or_create_a2_path()
        if not path_id:
            return {'status': 'failed', 'error': 'Could not create A2 path'}
        
        results = {
            'path_id': path_id,
            'total_lessons': len(A2_LESSONS),
            'created': 0,
            'failed': [],
            'lesson_ids': []
        }
        
        for i, lesson_title in enumerate(A2_LESSONS, 1):
            print(f"\n📖 Creating {i}/{len(A2_LESSONS)}: {lesson_title}")
            
            # Create lesson structure
            lesson_data = self.create_lesson_structure(lesson_title, i, path_id)
            
            try:
                # Insert lesson into database
                response = requests.post(f"{SUPABASE_URL}/rest/v1/lessons", json=lesson_data, headers=self.headers)
                
                if response.status_code == 201:
                    lesson_id = response.json()[0]['id']
                    results['lesson_ids'].append(lesson_id)
                    results['created'] += 1
                    print(f"✅ Created: {lesson_title}")
                else:
                    results['failed'].append(f"{lesson_title}: {response.status_code}")
                    print(f"❌ Failed: {lesson_title} - {response.status_code}")
                    
            except Exception as e:
                results['failed'].append(f"{lesson_title}: {str(e)}")
                print(f"❌ Error: {lesson_title} - {e}")
            
            # Rate limiting
            time.sleep(1)
        
        return results
    
    def display_creation_summary(self, results: Dict[str, Any]) -> None:
        """Display lesson creation summary"""
        print(f"\n📊 A2 LESSON CREATION SUMMARY:")
        print("=" * 50)
        print(f"  • Path ID: {results.get('path_id', 'N/A')}")
        print(f"  • Total Lessons: {results.get('total_lessons', 0)}")
        print(f"  • Successfully Created: {results.get('created', 0)}")
        print(f"  • Failed: {len(results.get('failed', []))}")
        
        if results.get('failed'):
            print(f"\n❌ FAILED LESSONS:")
            for failure in results['failed']:
                print(f"  • {failure}")
        
        if results.get('created', 0) > 0:
            print(f"\n✅ SUCCESS! Created {results['created']} A2 lessons")
            print(f"\n📋 NEXT STEPS:")
            print("1. ✅ A2 lesson structure created")
            print("2. 🔄 Run A2 vocabulary validator")
            print("3. 🔄 Run A2 conversation validator")
            print("4. 🔄 Run A2 grammar validator")
            print("5. 🔄 Run A2 exercises validator")
            print("6. 🎵 Generate A2 audio content")
            print("7. 📱 Test A2 lessons in iOS app")

def main():
    """Main function"""
    print("🎯 TAMIL A2 ELEMENTARY LESSON CREATOR")
    print("Building on proven A1 foundation")
    print("=" * 50)
    
    creator = A2LessonCreator()
    
    # Create all A2 lessons
    results = creator.create_all_a2_lessons()
    
    # Display summary
    creator.display_creation_summary(results)
    
    # Save path ID for validators
    if results.get('path_id'):
        with open('/tmp/a2_path_id.txt', 'w') as f:
            f.write(results['path_id'])
        print(f"\n💾 A2 Path ID saved for validators: {results['path_id']}")

if __name__ == "__main__":
    main()
