#!/usr/bin/env python3
"""
Restore Original A1 Lesson Order
Restores the A1 lessons to their original working order

This script restores the exact A1 lesson order that was working before.
"""

import requests
import time
from typing import Dict, List, Any

# Supabase configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# Tamil A1 Path ID
TAMIL_A1_PATH_ID = "6b427613-420f-4586-bce8-2773d722f0b4"

# Original A1 lesson order that was working
ORIGINAL_A1_ORDER = [
    "Basic Greetings and Introductions",
    "Family Members and Relationships", 
    "Numbers and Counting",
    "Colors and Descriptions",
    "Food and Dining",
    "Body Parts and Health",
    "Weather and Seasons",
    "Transportation",
    "Clothing and Shopping",
    "Common Verbs and Actions",
    "Personal Information and Identity",
    "Home and Living Spaces",
    "Daily Routines and Activities",
    "Shopping and Money",
    "Directions and Locations",
    "Health and Body",
    "Hobbies and Interests",
    "Work and Professions",
    "Education and School",
    "Technology and Communication",
    "Emotions and Feelings",
    "Festivals and Celebrations",
    "Animals and Nature",
    "Vegetables and Healthy Eating",
    "Days, Weeks, Months, and Time",
    "Local Transportation",
    "Travel and Long Distance",
    "Music and Movies",
    "Famous Landmarks",
    "Sports and Games"
]

class A1OrderRestorer:
    """Restores original A1 lesson order"""
    
    def __init__(self):
        self.headers = {
            'apikey': SUPABASE_KEY,
            'Authorization': f'Bearer {SUPABASE_KEY}',
            'Content-Type': 'application/json'
        }
    
    def get_a1_lessons(self) -> List[Dict[str, Any]]:
        """Get all A1 lessons"""
        print("🔍 Getting A1 lessons...")
        
        params = {
            'select': 'id,title,sequence_order',
            'path_id': f'eq.{TAMIL_A1_PATH_ID}',
            'order': 'sequence_order'
        }
        
        response = requests.get(f"{SUPABASE_URL}/rest/v1/lessons", headers=self.headers, params=params)
        
        if response.status_code == 200:
            lessons = response.json()
            print(f"✅ Found {len(lessons)} A1 lessons")
            return lessons
        else:
            print(f"❌ Error fetching lessons: {response.status_code}")
            return []
    
    def restore_original_order(self) -> Dict[str, Any]:
        """Restore the original A1 lesson order"""
        print("🔧 Restoring original A1 lesson order...")
        
        # Get current lessons
        current_lessons = self.get_a1_lessons()
        
        if not current_lessons:
            return {'status': 'failed', 'error': 'Could not fetch lessons'}
        
        # Create a mapping of title to lesson data
        lesson_map = {lesson['title']: lesson for lesson in current_lessons}
        
        fixed_count = 0
        error_count = 0
        
        # Restore original order
        for i, expected_title in enumerate(ORIGINAL_A1_ORDER, 1):
            if expected_title in lesson_map:
                lesson = lesson_map[expected_title]
                lesson_id = lesson['id']
                current_sequence = lesson.get('sequence_order')
                new_sequence = i
                
                if current_sequence != new_sequence:
                    try:
                        # Update sequence_order
                        update_data = {'sequence_order': new_sequence}
                        
                        response = requests.patch(
                            f"{SUPABASE_URL}/rest/v1/lessons?id=eq.{lesson_id}",
                            json=update_data,
                            headers=self.headers
                        )
                        
                        if response.status_code in [200, 204]:
                            print(f"✅ Fixed: {expected_title} ({current_sequence} → {new_sequence})")
                            fixed_count += 1
                        else:
                            print(f"❌ Failed: {expected_title} - {response.status_code}")
                            error_count += 1
                        
                        time.sleep(0.3)
                        
                    except Exception as e:
                        print(f"❌ Error: {expected_title} - {e}")
                        error_count += 1
                else:
                    print(f"✓ OK: {expected_title} (sequence: {new_sequence})")
            else:
                print(f"⚠️ Missing: {expected_title}")
                error_count += 1
        
        return {
            'fixed': fixed_count,
            'errors': error_count,
            'total_expected': len(ORIGINAL_A1_ORDER)
        }
    
    def validate_restoration(self) -> bool:
        """Validate that the restoration worked"""
        print("🔍 Validating A1 lesson order restoration...")
        
        params = {
            'select': 'id,title,sequence_order',
            'path_id': f'eq.{TAMIL_A1_PATH_ID}',
            'order': 'sequence_order'
        }
        
        response = requests.get(f"{SUPABASE_URL}/rest/v1/lessons", headers=self.headers, params=params)
        
        if response.status_code != 200:
            print(f"❌ Error validating: {response.status_code}")
            return False
        
        lessons = response.json()
        
        print(f"📊 Validation Results:")
        print(f"  • Total lessons: {len(lessons)}")
        
        # Check first 5 lessons specifically
        print(f"  • First 5 lessons:")
        for i, lesson in enumerate(lessons[:5]):
            title = lesson.get('title', 'Unknown')
            seq = lesson.get('sequence_order')
            expected_title = ORIGINAL_A1_ORDER[i] if i < len(ORIGINAL_A1_ORDER) else 'Unknown'
            
            status = "✅" if title == expected_title and seq == i + 1 else "❌"
            print(f"    Index {i}: {status} seq={seq} - {title}")
            
            if seq is None:
                print(f"      ⚠️ NULL SEQUENCE ORDER AT INDEX {i}")
                return False
        
        # Check specifically index 2
        if len(lessons) > 2:
            lesson_2 = lessons[2]
            seq_2 = lesson_2.get('sequence_order')
            title_2 = lesson_2.get('title', 'Unknown')
            expected_2 = ORIGINAL_A1_ORDER[2]
            
            print(f"\n🎯 Index 2 Check:")
            print(f"  • Expected: {expected_2}")
            print(f"  • Actual: {title_2}")
            print(f"  • Sequence: {seq_2}")
            print(f"  • Match: {'✅' if title_2 == expected_2 and seq_2 == 3 else '❌'}")
            
            if seq_2 is None or title_2 != expected_2:
                return False
        
        return True

def main():
    """Main function"""
    print("🎯 A1 LESSON ORDER RESTORER")
    print("Restoring original working A1 lesson order")
    print("=" * 50)
    
    restorer = A1OrderRestorer()
    
    # Restore original order
    results = restorer.restore_original_order()
    
    # Validate restoration
    validation_passed = restorer.validate_restoration()
    
    # Display results
    print(f"\n📊 RESTORATION RESULTS:")
    print(f"  • Fixed: {results.get('fixed', 0)}")
    print(f"  • Errors: {results.get('errors', 0)}")
    print(f"  • Expected: {results.get('total_expected', 0)}")
    print(f"  • Validation: {'✅ PASSED' if validation_passed else '❌ FAILED'}")
    
    if validation_passed:
        print(f"\n🎉 SUCCESS! A1 lessons restored to original working order")
        print(f"📱 iOS app should work now")
    else:
        print(f"\n❌ Restoration failed - check validation output")

if __name__ == "__main__":
    main()
