#!/usr/bin/env python3
"""
Fix Sequence Order for NIRA Lessons
Fixes the sequence_order issues causing iOS app crashes

This script:
1. Gets all Tamil A1 lessons
2. Filters out non-A1 lessons (B1, B2, etc.)
3. Assigns proper consecutive sequence_order (1, 2, 3, ...)
4. Updates the database
"""

import requests
import time
from typing import Dict, List, Any

# Supabase configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# Tamil A1 Path ID
TAMIL_A1_PATH_ID = "6b427613-420f-4586-bce8-2773d722f0b4"

class SequenceOrderFixer:
    """Fixes sequence order issues"""
    
    def __init__(self):
        self.headers = {
            'apikey': SUPABASE_KEY,
            'Authorization': f'Bearer {SUPABASE_KEY}',
            'Content-Type': 'application/json'
        }
    
    def get_all_lessons(self) -> List[Dict[str, Any]]:
        """Get all lessons in the Tamil A1 path"""
        print("🔍 Fetching all lessons in Tamil A1 path...")
        
        params = {
            'select': 'id,title,sequence_order,difficulty_level,created_at',
            'path_id': f'eq.{TAMIL_A1_PATH_ID}',
            'order': 'created_at'
        }
        
        response = requests.get(f"{SUPABASE_URL}/rest/v1/lessons", headers=self.headers, params=params)
        
        if response.status_code == 200:
            lessons = response.json()
            print(f"✅ Found {len(lessons)} lessons")
            return lessons
        else:
            print(f"❌ Error fetching lessons: {response.status_code}")
            return []
    
    def filter_a1_lessons(self, lessons: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Filter to keep only A1 lessons (remove B1, B2, etc.)"""
        print("🔍 Filtering A1 lessons...")
        
        a1_lessons = []
        non_a1_lessons = []
        
        for lesson in lessons:
            title = lesson.get('title', '')
            difficulty = lesson.get('difficulty_level', 1)
            
            # Check if it's a non-A1 lesson
            if any(level in title for level in ['(A2)', '(B1)', '(B2)', '(C1)', '(C2)']):
                non_a1_lessons.append(lesson)
            elif difficulty > 1:  # difficulty_level > 1 means not A1
                non_a1_lessons.append(lesson)
            else:
                a1_lessons.append(lesson)
        
        print(f"✅ Found {len(a1_lessons)} A1 lessons")
        print(f"⚠️ Found {len(non_a1_lessons)} non-A1 lessons to remove")
        
        return a1_lessons, non_a1_lessons
    
    def remove_non_a1_lessons(self, non_a1_lessons: List[Dict[str, Any]]) -> int:
        """Remove non-A1 lessons from the A1 path"""
        print("🗑️ Removing non-A1 lessons from A1 path...")
        
        removed_count = 0
        
        for lesson in non_a1_lessons:
            try:
                lesson_id = lesson['id']
                title = lesson.get('title', 'Unknown')
                
                # Delete the lesson
                response = requests.delete(
                    f"{SUPABASE_URL}/rest/v1/lessons?id=eq.{lesson_id}",
                    headers=self.headers
                )
                
                if response.status_code in [200, 204]:
                    print(f"✅ Removed: {title}")
                    removed_count += 1
                else:
                    print(f"❌ Failed to remove: {title} - {response.status_code}")
                
                time.sleep(0.5)
                
            except Exception as e:
                print(f"❌ Error removing lesson: {e}")
        
        print(f"✅ Removed {removed_count} non-A1 lessons")
        return removed_count
    
    def fix_sequence_order(self, a1_lessons: List[Dict[str, Any]]) -> int:
        """Fix sequence order for A1 lessons"""
        print("🔧 Fixing sequence order for A1 lessons...")
        
        # Sort lessons by current sequence_order, then by title for consistency
        a1_lessons.sort(key=lambda x: (x.get('sequence_order', 999), x.get('title', '')))
        
        fixed_count = 0
        
        for i, lesson in enumerate(a1_lessons, 1):
            lesson_id = lesson['id']
            title = lesson.get('title', 'Unknown')
            current_sequence = lesson.get('sequence_order')
            new_sequence = i
            
            if current_sequence != new_sequence:
                try:
                    # Update sequence_order
                    update_data = {'sequence_order': new_sequence}
                    
                    response = requests.patch(
                        f"{SUPABASE_URL}/rest/v1/lessons?id=eq.{lesson_id}",
                        json=update_data,
                        headers=self.headers
                    )
                    
                    if response.status_code in [200, 204]:
                        print(f"✅ Fixed: {title} ({current_sequence} → {new_sequence})")
                        fixed_count += 1
                    else:
                        print(f"❌ Failed to fix: {title} - {response.status_code}")
                    
                    time.sleep(0.5)
                    
                except Exception as e:
                    print(f"❌ Error fixing lesson: {e}")
            else:
                print(f"✓ OK: {title} (sequence: {new_sequence})")
        
        print(f"✅ Fixed {fixed_count} sequence orders")
        return fixed_count
    
    def validate_fix(self) -> bool:
        """Validate that the fix worked"""
        print("🔍 Validating sequence order fix...")
        
        params = {
            'select': 'id,title,sequence_order',
            'path_id': f'eq.{TAMIL_A1_PATH_ID}',
            'order': 'sequence_order'
        }
        
        response = requests.get(f"{SUPABASE_URL}/rest/v1/lessons", headers=self.headers, params=params)
        
        if response.status_code != 200:
            print(f"❌ Error validating: {response.status_code}")
            return False
        
        lessons = response.json()
        
        print(f"📊 Validation Results:")
        print(f"  • Total lessons: {len(lessons)}")
        
        # Check for consecutive sequence numbers
        expected_sequences = list(range(1, len(lessons) + 1))
        actual_sequences = [lesson.get('sequence_order') for lesson in lessons]
        
        if actual_sequences == expected_sequences:
            print(f"  • ✅ Sequence order: Perfect (1 to {len(lessons)})")
            print(f"  • ✅ No gaps or duplicates")
            return True
        else:
            print(f"  • ❌ Sequence order: Issues found")
            print(f"  • Expected: {expected_sequences[:10]}...")
            print(f"  • Actual: {actual_sequences[:10]}...")
            return False
    
    def fix_all_issues(self) -> Dict[str, Any]:
        """Fix all sequence order issues"""
        print("🚀 SEQUENCE ORDER FIXER")
        print("Fixing iOS app crashes caused by sequence order issues")
        print("=" * 60)
        
        # Get all lessons
        all_lessons = self.get_all_lessons()
        if not all_lessons:
            return {'status': 'failed', 'error': 'Could not fetch lessons'}
        
        # Filter A1 vs non-A1 lessons
        a1_lessons, non_a1_lessons = self.filter_a1_lessons(all_lessons)
        
        results = {
            'total_lessons_found': len(all_lessons),
            'a1_lessons': len(a1_lessons),
            'non_a1_lessons': len(non_a1_lessons),
            'removed_lessons': 0,
            'fixed_sequences': 0,
            'validation_passed': False
        }
        
        # Remove non-A1 lessons
        if non_a1_lessons:
            results['removed_lessons'] = self.remove_non_a1_lessons(non_a1_lessons)
        
        # Fix sequence order for A1 lessons
        if a1_lessons:
            results['fixed_sequences'] = self.fix_sequence_order(a1_lessons)
        
        # Validate the fix
        results['validation_passed'] = self.validate_fix()
        
        return results

def main():
    """Main function"""
    print("🎯 SEQUENCE ORDER FIXER")
    print("Fixing iOS app crashes")
    print("=" * 40)
    
    fixer = SequenceOrderFixer()
    
    # Fix all issues
    results = fixer.fix_all_issues()
    
    # Display results
    print(f"\n📊 FIX RESULTS:")
    print(f"  • Total lessons found: {results.get('total_lessons_found', 0)}")
    print(f"  • A1 lessons: {results.get('a1_lessons', 0)}")
    print(f"  • Non-A1 lessons: {results.get('non_a1_lessons', 0)}")
    print(f"  • Removed lessons: {results.get('removed_lessons', 0)}")
    print(f"  • Fixed sequences: {results.get('fixed_sequences', 0)}")
    print(f"  • Validation passed: {'✅' if results.get('validation_passed') else '❌'}")
    
    if results.get('validation_passed'):
        print(f"\n🎉 SUCCESS! iOS app should work now")
        print(f"📱 Try loading the Tamil lessons in your iOS app")
    else:
        print(f"\n❌ Issues remain - check the validation output")

if __name__ == "__main__":
    main()
