#!/usr/bin/env python3
"""
Systematic Conversation Validator for NIRA
Step 2: Validate and fix ONLY conversations for all 30 lessons

This script:
1. Validates conversations against quality checklist
2. Identifies placeholder/poor content
3. Generates new authentic Tamil conversations
4. Updates only conversation section
5. Moves to next lesson

Following the systematic approach: Vocabulary ✅ → Conversations → Grammar → Exercises
"""

import json
import requests
import time
from typing import Dict, List, Any
import google.generativeai as genai

# API Configuration
GEMINI_API_KEY = "AIzaSyAYasSocME9nPr7HP625EEx4cofwm5MH3Q"
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# Initialize Gemini
genai.configure(api_key=GEMINI_API_KEY)
gemini_model = genai.GenerativeModel('gemini-2.0-flash-exp')

# Tamil A1 Path ID
TAMIL_A1_PATH_ID = "6b427613-420f-4586-bce8-2773d722f0b4"

class SystematicConversationValidator:
    """Validates and fixes conversations systematically"""
    
    def __init__(self):
        self.headers = {
            'apikey': SUPABASE_KEY,
            'Authorization': f'Bearer {SUPABASE_KEY}',
            'Content-Type': 'application/json'
        }
    
    def get_all_lessons(self) -> List[Dict[str, Any]]:
        """Get all Tamil A1 lessons"""
        print("🔍 Fetching all Tamil A1 lessons...")
        
        params = {
            'select': 'id,title,sequence_order,content_metadata',
            'path_id': f'eq.{TAMIL_A1_PATH_ID}',
            'order': 'sequence_order'
        }
        
        response = requests.get(f"{SUPABASE_URL}/rest/v1/lessons", headers=self.headers, params=params)
        
        if response.status_code == 200:
            lessons = response.json()
            print(f"✅ Found {len(lessons)} lessons")
            return lessons
        else:
            print(f"❌ Error fetching lessons: {response.status_code}")
            return []
    
    def validate_conversation_quality(self, conversations: List[Dict[str, Any]], lesson_title: str) -> Dict[str, Any]:
        """Validate conversations against quality checklist"""
        issues = []
        
        # Check count
        if len(conversations) < 15:
            issues.append(f"Insufficient conversations: {len(conversations)}/15")
        
        # Check for placeholder content
        placeholder_count = 0
        for conv in conversations:
            title = conv.get('title', '').lower()
            scenario = conv.get('scenario', '').lower()
            if any(placeholder in title for placeholder in ['conversation', 'discussing', 'talking about']):
                placeholder_count += 1
        
        if placeholder_count > 5:  # Allow some generic titles
            issues.append(f"Generic conversation titles: {placeholder_count} items")
        
        # Check for topic relevance
        topic_words = lesson_title.lower().split()
        relevant_count = 0
        for conv in conversations:
            title = conv.get('title', '').lower()
            scenario = conv.get('scenario', '').lower()
            if any(topic_word in title or topic_word in scenario for topic_word in topic_words):
                relevant_count += 1
        
        relevance_score = (relevant_count / len(conversations)) * 100 if conversations else 0
        
        # Check for pronunciations
        missing_pronunciations = 0
        for conv in conversations:
            exchanges = conv.get('exchanges', [])
            for exchange in exchanges:
                if not exchange.get('pronunciation'):
                    missing_pronunciations += 1
                    break
        
        if missing_pronunciations > 0:
            issues.append(f"Missing pronunciations: {missing_pronunciations} conversations")
        
        # Check for duplicates
        titles = [conv.get('title', '') for conv in conversations]
        duplicates = len(titles) - len(set(titles))
        if duplicates > 0:
            issues.append(f"Duplicate conversation titles: {duplicates} items")
        
        # Check for realistic exchanges
        short_conversations = 0
        for conv in conversations:
            exchanges = conv.get('exchanges', [])
            if len(exchanges) < 2:
                short_conversations += 1
        
        if short_conversations > 0:
            issues.append(f"Too short conversations: {short_conversations} items")
        
        return {
            'total_items': len(conversations),
            'placeholder_count': placeholder_count,
            'relevance_score': relevance_score,
            'missing_pronunciations': missing_pronunciations,
            'duplicates': duplicates,
            'short_conversations': short_conversations,
            'issues': issues,
            'needs_regeneration': len(issues) > 0 or relevance_score < 60
        }
    
    def generate_authentic_conversations(self, lesson_title: str) -> List[Dict[str, Any]]:
        """Generate authentic Tamil conversations for the lesson topic"""
        print(f"🔄 Generating authentic conversations for: {lesson_title}")
        
        prompt = f"""
        Generate exactly 15 authentic Tamil conversations for the A1 lesson: "{lesson_title}"

        Requirements:
        - All conversations must be directly related to {lesson_title}
        - Use authentic Chennai Tamil
        - A1 beginner level difficulty
        - Each conversation has 2-4 realistic exchanges
        - Include proper romanized pronunciations
        - Realistic scenarios and natural dialogue
        - No placeholder or generic content

        Return as valid JSON array:
        [
            {{
                "title": "specific_conversation_title_related_to_topic",
                "scenario": "realistic_scenario_description",
                "difficulty": "beginner",
                "exchanges": [
                    {{
                        "speaker": "Person A",
                        "text": "authentic_tamil_text",
                        "translation": "english_translation",
                        "pronunciation": "romanized_pronunciation",
                        "audio_url": "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/{lesson_title.lower().replace(' ', '_')}/conv_{{conv_index:02d}}_{{exchange_index:02d}}.mp3"
                    }},
                    {{
                        "speaker": "Person B",
                        "text": "authentic_tamil_response",
                        "translation": "english_translation",
                        "pronunciation": "romanized_pronunciation", 
                        "audio_url": "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/{lesson_title.lower().replace(' ', '_')}/conv_{{conv_index:02d}}_{{exchange_index:02d}}.mp3"
                    }}
                ]
            }}
        ]

        Generate exactly 15 conversations. Make them realistic and practical for {lesson_title}.
        """
        
        try:
            response = gemini_model.generate_content(prompt)
            content = response.text
            
            # Clean and parse JSON
            if "```json" in content:
                content = content.split("```json")[1].split("```")[0]
            elif "```" in content:
                content = content.split("```")[1].split("```")[0]
            
            conversations = json.loads(content.strip())
            
            # Validate we got exactly 15 conversations
            if len(conversations) != 15:
                print(f"⚠️ Generated {len(conversations)} conversations, expected 15")
                # Truncate or pad as needed
                if len(conversations) > 15:
                    conversations = conversations[:15]
                elif len(conversations) < 15:
                    # Duplicate some conversations to reach 15
                    while len(conversations) < 15:
                        conversations.append(conversations[len(conversations) % len(conversations)])
            
            # Add proper audio URLs with correct indexing
            lesson_slug = lesson_title.lower().replace(' ', '_').replace('&', 'and')
            for conv_idx, conv in enumerate(conversations, 1):
                for ex_idx, exchange in enumerate(conv.get('exchanges', []), 1):
                    exchange['audio_url'] = f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/{lesson_slug}/conv_{conv_idx:02d}_{ex_idx:02d}.mp3"
            
            print(f"✅ Generated {len(conversations)} authentic conversations")
            return conversations
            
        except Exception as e:
            print(f"❌ Failed to generate conversations: {e}")
            return []
    
    def update_lesson_conversations(self, lesson_id: str, new_conversations: List[Dict[str, Any]], existing_metadata: Dict[str, Any]) -> bool:
        """Update only the conversations section of a lesson"""
        try:
            # Keep existing vocabulary, grammar, exercises - only update conversations
            updated_metadata = existing_metadata.copy()
            updated_metadata['conversations'] = new_conversations
            
            update_url = f"{SUPABASE_URL}/rest/v1/lessons"
            params = {'id': f'eq.{lesson_id}'}
            
            update_data = {
                'content_metadata': updated_metadata,
                'updated_at': 'now()'
            }
            
            response = requests.patch(update_url, json=update_data, headers=self.headers, params=params)
            
            if response.status_code in [200, 204]:
                print(f"✅ Conversations updated successfully")
                return True
            else:
                print(f"❌ Database update failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Error updating conversations: {e}")
            return False
    
    def process_all_conversations(self) -> Dict[str, Any]:
        """Process conversations for all 30 lessons systematically"""
        print("🚀 SYSTEMATIC CONVERSATION VALIDATOR")
        print("Step 2: Validating and fixing conversations for all 30 lessons")
        print("=" * 70)
        
        lessons = self.get_all_lessons()
        if not lessons:
            return {}
        
        results = {
            'total_lessons': len(lessons),
            'processed': 0,
            'conversations_fixed': 0,
            'conversations_good': 0,
            'failed': [],
            'lesson_details': []
        }
        
        for i, lesson in enumerate(lessons, 1):
            print(f"\n📖 Processing {i}/{len(lessons)}: {lesson['title']}")
            
            metadata = lesson.get('content_metadata', {})
            conversations = metadata.get('conversations', [])
            
            # Validate current conversations
            validation = self.validate_conversation_quality(conversations, lesson['title'])
            
            print(f"📊 Current conversations: {validation['total_items']} items")
            print(f"📊 Issues: {', '.join(validation['issues']) if validation['issues'] else 'None'}")
            print(f"📊 Relevance score: {validation['relevance_score']:.1f}%")
            
            if validation['needs_regeneration']:
                print(f"🔄 Regenerating conversations...")
                
                # Generate new conversations
                new_conversations = self.generate_authentic_conversations(lesson['title'])
                
                if new_conversations:
                    # Update lesson
                    if self.update_lesson_conversations(lesson['id'], new_conversations, metadata):
                        results['conversations_fixed'] += 1
                        print(f"✅ {lesson['title']} conversations fixed")
                    else:
                        results['failed'].append(lesson['title'])
                        print(f"❌ {lesson['title']} conversations update failed")
                else:
                    results['failed'].append(lesson['title'])
                    print(f"❌ {lesson['title']} conversations generation failed")
            else:
                results['conversations_good'] += 1
                print(f"✅ {lesson['title']} conversations are good")
            
            results['processed'] += 1
            results['lesson_details'].append({
                'title': lesson['title'],
                'validation': validation,
                'action': 'fixed' if validation['needs_regeneration'] else 'good'
            })
            
            # Rate limiting
            time.sleep(2)
        
        return results

def main():
    """Main function"""
    print("🎯 SYSTEMATIC CONVERSATION VALIDATOR")
    print("Step 2 of 4: Conversation validation and generation")
    print("=" * 60)
    
    validator = SystematicConversationValidator()
    
    # Process all conversations
    results = validator.process_all_conversations()
    
    # Display results
    print(f"\n📊 CONVERSATION PROCESSING RESULTS:")
    print(f"  • Total Lessons: {results.get('total_lessons', 0)}")
    print(f"  • Processed: {results.get('processed', 0)}")
    print(f"  • Conversations Fixed: {results.get('conversations_fixed', 0)}")
    print(f"  • Conversations Good: {results.get('conversations_good', 0)}")
    print(f"  • Failed: {len(results.get('failed', []))}")
    
    if results.get('failed'):
        print(f"\n❌ FAILED LESSONS:")
        for failure in results['failed']:
            print(f"  • {failure}")
    
    print(f"\n📋 NEXT STEPS:")
    print("1. ✅ Vocabulary validation complete")
    print("2. ✅ Conversation validation complete")
    print("3. 🔄 Next: Run grammar validator")
    print("4. 🔄 Finally: Run exercises validator")
    print("5. 🎵 Generate audio for all content")

if __name__ == "__main__":
    main()
