#!/usr/bin/env python3
"""
C2 Vocabulary Validator for NIRA
C2 Mastery level Tamil lessons

This script creates and validates C2 mastery level vocabulary:
- 50 vocabulary items per lesson
- Native-level sophisticated vocabulary
- Literary and classical terminology
- Mastery level difficulty
"""

import json
import requests
import time
from typing import Dict, List, Any
import google.generativeai as genai

# API Configuration
GEMINI_API_KEY = "AIzaSyAYasSocME9nPr7HP625EEx4cofwm5MH3Q"
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# Initialize Gemini
genai.configure(api_key=GEMINI_API_KEY)
gemini_model = genai.GenerativeModel('gemini-2.0-flash-exp')

# Tamil C2 Path ID
TAMIL_C2_PATH_ID = "c2-mastery-path-id"

# C2 Mastery Tamil Lessons (30 lessons)
C2_LESSONS = [
    "Classical Tamil Literature and Poetic Traditions",
    "Advanced Linguistic Theory and Tamil Grammar",
    "Tamil Philosophy and Ancient Wisdom Traditions",
    "Contemporary Tamil Literary Criticism",
    "Tamil Cultural Heritage and Historical Linguistics",
    "Advanced Tamil Prosody and Metrical Analysis",
    "Tamil Rhetoric and Classical Oratory",
    "Modern Tamil Poetry and Literary Innovation",
    "Tamil Dialectology and Regional Variations",
    "Tamil Epigraphy and Historical Documentation",
    "Tamil Musicology and Classical Arts",
    "Tamil Drama and Theatrical Traditions",
    "Tamil Journalism and Media Language",
    "Tamil Translation Theory and Practice",
    "Tamil Computational Linguistics",
    "Tamil Sociolinguistics and Language Policy",
    "Tamil Literary Theory and Criticism",
    "Tamil Folklore and Oral Traditions",
    "Tamil Academic Writing and Scholarly Discourse",
    "Tamil Legal Language and Jurisprudence",
    "Tamil Scientific Terminology and Technical Writing",
    "Tamil Religious Literature and Spiritual Texts",
    "Tamil Historical Narratives and Chronicling",
    "Tamil Comparative Literature Studies",
    "Tamil Stylistics and Discourse Analysis",
    "Tamil Lexicography and Dictionary Making",
    "Tamil Pedagogy and Language Teaching",
    "Tamil Digital Humanities and Corpus Linguistics",
    "Tamil Cultural Studies and Identity",
    "Tamil Language Planning and Standardization"
]

class C2VocabularyValidator:
    """C2 vocabulary validator and creator"""
    
    def __init__(self):
        self.headers = {
            'apikey': SUPABASE_KEY,
            'Authorization': f'Bearer {SUPABASE_KEY}',
            'Content-Type': 'application/json'
        }
    
    def create_c2_lesson_with_vocabulary(self, title: str, sequence: int) -> bool:
        """Create C2 lesson with vocabulary directly"""
        print(f"\n🔧 CREATING C2 LESSON: {title}")
        
        vocabulary = self.generate_c2_vocabulary(title)
        
        if not vocabulary:
            print(f"❌ Failed to generate vocabulary for {title}")
            return False
        
        lesson_data = {
            'path_id': TAMIL_C2_PATH_ID,
            'title': title,
            'description': f'Mastery level Tamil lesson covering {title.lower()} with native-level vocabulary and classical literary concepts',
            'lesson_type': 'comprehensive',
            'difficulty_level': 6,  # C2 level
            'estimated_duration': 120,
            'sequence_order': sequence,
            'learning_objectives': [
                f'Master native-level vocabulary in {title.lower()}',
                f'Engage in sophisticated literary discourse about {title.lower()}',
                f'Apply classical and contemporary frameworks in {title.lower()} context',
                f'Create original content and analysis in {title.lower()} domain'
            ],
            'vocabulary_focus': None,
            'grammar_concepts': [],
            'cultural_notes': '',
            'prerequisite_lessons': [],
            'content_metadata': {
                'title': title,
                'description': f'Mastery level Tamil lesson covering {title.lower()}',
                'vocabulary': vocabulary,
                'conversations': [],
                'grammar_points': [],
                'exercises': [],
                'estimated_duration': 120,
                'learning_objectives': [
                    f'Master native-level vocabulary in {title.lower()}',
                    f'Engage in sophisticated literary discourse about {title.lower()}',
                    f'Apply classical and contemporary frameworks in {title.lower()} context',
                    f'Create original content and analysis in {title.lower()} domain'
                ]
            },
            'is_active': True,
            'audio_url': None,
            'audio_metadata': {},
            'has_audio': False
        }
        
        try:
            response = requests.post(f"{SUPABASE_URL}/rest/v1/lessons", json=lesson_data, headers=self.headers)
            
            if response.status_code == 201:
                print(f"✅ Created C2 lesson: {title}")
                print(f"📊 Generated: {len(vocabulary)} vocabulary items")
                return True
            else:
                print(f"❌ Database insert failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Error creating lesson: {e}")
            return False
    
    def generate_c2_vocabulary(self, lesson_title: str) -> List[Dict[str, Any]]:
        """Generate C2 level Tamil vocabulary"""
        print(f"🔄 Generating C2 vocabulary for: {lesson_title}")
        
        prompt = f"""
        Generate exactly 50 authentic Tamil vocabulary items for the C2 mastery lesson: "{lesson_title}"

        Requirements:
        - C2 mastery level difficulty
        - Native-level sophisticated vocabulary
        - Classical Tamil and literary terminology
        - Academic and scholarly language
        - Rare and archaic terms with modern usage
        - Use authentic Tamil with classical and contemporary influences
        - Each word must be unique

        Return as valid JSON array with 50 items for mastery-level complexity.
        """
        
        try:
            response = gemini_model.generate_content(prompt)
            content = response.text
            
            if "```json" in content:
                content = content.split("```json")[1].split("```")[0]
            elif "```" in content:
                content = content.split("```")[1].split("```")[0]
            
            vocabulary = json.loads(content.strip())
            
            if len(vocabulary) != 50:
                vocabulary = vocabulary[:50] if len(vocabulary) > 50 else vocabulary + vocabulary[:50-len(vocabulary)]
            
            lesson_slug = lesson_title.lower().replace(' ', '_').replace('&', 'and')
            for i, vocab in enumerate(vocabulary, 1):
                vocab['word_audio_url'] = f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/c2/{lesson_slug}/vocab_{i:02d}_word.mp3"
                vocab['example_audio_url'] = f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/c2/{lesson_slug}/vocab_{i:02d}_example.mp3"
            
            print(f"✅ Generated {len(vocabulary)} C2 vocabulary items")
            return vocabulary
            
        except Exception as e:
            print(f"❌ Failed to generate vocabulary: {e}")
            return []
    
    def create_all_c2_lessons(self) -> Dict[str, Any]:
        """Create all 30 C2 lessons with vocabulary"""
        print("🚀 C2 MASTERY VOCABULARY VALIDATOR")
        print("Creating 30 mastery Tamil lessons")
        print("=" * 60)
        
        results = {'total_lessons': len(C2_LESSONS), 'created': 0, 'failed': []}
        
        for i, lesson_title in enumerate(C2_LESSONS, 1):
            print(f"\n📖 Processing {i}/{len(C2_LESSONS)}: {lesson_title}")
            
            if self.create_c2_lesson_with_vocabulary(lesson_title, i):
                results['created'] += 1
            else:
                results['failed'].append(lesson_title)
            
            time.sleep(2)
        
        return results

def main():
    validator = C2VocabularyValidator()
    results = validator.create_all_c2_lessons()
    
    print(f"\n📊 C2 RESULTS: Created {results['created']}/{results['total_lessons']} lessons")
    if results['failed']:
        print(f"❌ Failed: {len(results['failed'])} lessons")

if __name__ == "__main__":
    main()
