#!/usr/bin/env python3
"""
Robust Lesson Completer for NIRA
Uses the existing working comprehensive_lesson_generator.py to complete lessons

This approach leverages the proven working generator instead of creating new content.
"""

import os
import sys
import json
import requests
import time
from typing import Dict, List, Any
import subprocess

# Add the Scripts directory to Python path
sys.path.append('/Users/<USER>/Documents/NIRA/NIRA/Scripts')

# Supabase configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# Tamil A1 Path ID
TAMIL_A1_PATH_ID = "6b427613-420f-4586-bce8-2773d722f0b4"

class RobustLessonCompleter:
    """Robust lesson completer using existing proven generator"""
    
    def __init__(self):
        self.headers = {
            'apikey': SUPABASE_KEY,
            'Authorization': f'Bearer {SUPABASE_KEY}',
            'Content-Type': 'application/json'
        }
    
    def get_incomplete_lessons(self) -> List[Dict[str, Any]]:
        """Get lessons that need completion"""
        print("🔍 Fetching incomplete Tamil A1 lessons...")
        
        params = {
            'select': 'id,title,sequence_order,content_metadata',
            'path_id': f'eq.{TAMIL_A1_PATH_ID}',
            'order': 'sequence_order'
        }
        
        response = requests.get(f"{SUPABASE_URL}/rest/v1/lessons", headers=self.headers, params=params)
        
        if response.status_code != 200:
            print(f"❌ Error fetching lessons: {response.status_code}")
            return []
        
        all_lessons = response.json()
        
        # Find incomplete lessons
        incomplete_lessons = []
        for lesson in all_lessons:
            metadata = lesson.get('content_metadata', {})
            vocab_count = len(metadata.get('vocabulary', []))
            conv_count = len(metadata.get('conversations', []))
            grammar_count = len(metadata.get('grammar_points', []))
            exercise_count = len(metadata.get('exercises', []))
            
            # Check if incomplete (missing any major section)
            if conv_count < 15 or grammar_count < 10 or exercise_count < 5:
                incomplete_lessons.append(lesson)
                print(f"📋 {lesson['title']}: {vocab_count}v, {conv_count}c, {grammar_count}g, {exercise_count}e")
        
        print(f"✅ Found {len(incomplete_lessons)} incomplete lessons")
        return incomplete_lessons
    
    def complete_single_lesson(self, lesson: Dict[str, Any]) -> bool:
        """Complete a single lesson using the comprehensive generator"""
        print(f"\n🔧 COMPLETING: {lesson['title']}")
        print(f"Sequence Order: {lesson['sequence_order']}")
        
        try:
            # Import the working generator
            from comprehensive_lesson_generator import ComprehensiveLessonGenerator
            
            # Create generator instance
            generator = ComprehensiveLessonGenerator()
            
            # Generate comprehensive content
            lesson_data = generator.generate_comprehensive_lesson_content(
                lesson['title'], 
                lesson['sequence_order']
            )
            
            if lesson_data and lesson_data.get('status') != 'minimal_fallback':
                # Update the database
                success = generator.update_lesson_in_database(lesson_data)
                
                if success:
                    print(f"✅ Successfully completed {lesson['title']}")
                    
                    # Verify the update
                    vocab_count = len(lesson_data.get('vocabulary', []))
                    conv_count = len(lesson_data.get('conversations', []))
                    grammar_count = len(lesson_data.get('grammar_points', []))
                    exercise_count = len(lesson_data.get('exercises', []))
                    
                    print(f"📊 Generated: {vocab_count}v, {conv_count}c, {grammar_count}g, {exercise_count}e")
                    
                    return True
                else:
                    print(f"❌ Database update failed for {lesson['title']}")
                    return False
            else:
                print(f"❌ Content generation failed for {lesson['title']}")
                return False
                
        except Exception as e:
            print(f"❌ Error completing {lesson['title']}: {e}")
            return False
    
    def validate_lesson_completion(self, lesson_id: str, title: str) -> bool:
        """Validate that a lesson was completed properly"""
        print(f"🔍 Validating {title}...")
        
        params = {
            'select': 'content_metadata',
            'id': f'eq.{lesson_id}'
        }
        
        response = requests.get(f"{SUPABASE_URL}/rest/v1/lessons", headers=self.headers, params=params)
        
        if response.status_code != 200:
            print(f"❌ Failed to fetch lesson for validation")
            return False
        
        lessons = response.json()
        if not lessons:
            print(f"❌ Lesson not found")
            return False
        
        metadata = lessons[0].get('content_metadata', {})
        
        # Check completion criteria
        vocab_count = len(metadata.get('vocabulary', []))
        conv_count = len(metadata.get('conversations', []))
        grammar_count = len(metadata.get('grammar_points', []))
        exercise_count = len(metadata.get('exercises', []))
        
        print(f"📊 Current content: {vocab_count}v, {conv_count}c, {grammar_count}g, {exercise_count}e")
        
        # Check if complete
        is_complete = (vocab_count >= 25 and conv_count >= 15 and 
                      grammar_count >= 10 and exercise_count >= 5)
        
        if is_complete:
            print(f"✅ {title} validation passed")
        else:
            print(f"❌ {title} validation failed")
            
        return is_complete
    
    def complete_all_lessons(self) -> Dict[str, Any]:
        """Complete all incomplete lessons"""
        print("🚀 ROBUST LESSON COMPLETER")
        print("Using proven comprehensive_lesson_generator.py")
        print("=" * 60)
        
        incomplete_lessons = self.get_incomplete_lessons()
        
        if not incomplete_lessons:
            print("🎉 All lessons are already complete!")
            return {'status': 'all_complete'}
        
        results = {
            'total_lessons': len(incomplete_lessons),
            'processed': 0,
            'successful': 0,
            'failed': [],
            'completed_lessons': []
        }
        
        for i, lesson in enumerate(incomplete_lessons, 1):
            print(f"\n📖 Processing {i}/{len(incomplete_lessons)}: {lesson['title']}")
            
            # Complete the lesson
            if self.complete_single_lesson(lesson):
                # Validate completion
                if self.validate_lesson_completion(lesson['id'], lesson['title']):
                    results['successful'] += 1
                    results['completed_lessons'].append(lesson['title'])
                    print(f"🎉 {lesson['title']} FULLY COMPLETED")
                else:
                    results['failed'].append(f"{lesson['title']}: Validation failed")
                    print(f"⚠️ {lesson['title']} processed but validation failed")
            else:
                results['failed'].append(f"{lesson['title']}: Processing failed")
                print(f"❌ {lesson['title']} processing failed")
            
            results['processed'] += 1
            
            # Rate limiting between lessons
            print(f"⏳ Waiting 5 seconds before next lesson...")
            time.sleep(5)
        
        return results
    
    def run_final_validation(self) -> None:
        """Run final validation on all lessons"""
        print("\n🔍 RUNNING FINAL VALIDATION ON ALL LESSONS")
        print("=" * 50)
        
        params = {
            'select': 'id,title,sequence_order,content_metadata',
            'path_id': f'eq.{TAMIL_A1_PATH_ID}',
            'order': 'sequence_order'
        }
        
        response = requests.get(f"{SUPABASE_URL}/rest/v1/lessons", headers=self.headers, params=params)
        
        if response.status_code != 200:
            print(f"❌ Error fetching lessons for validation")
            return
        
        all_lessons = response.json()
        
        complete_count = 0
        incomplete_count = 0
        
        for lesson in all_lessons:
            metadata = lesson.get('content_metadata', {})
            vocab_count = len(metadata.get('vocabulary', []))
            conv_count = len(metadata.get('conversations', []))
            grammar_count = len(metadata.get('grammar_points', []))
            exercise_count = len(metadata.get('exercises', []))
            
            is_complete = (vocab_count >= 25 and conv_count >= 15 and 
                          grammar_count >= 10 and exercise_count >= 5)
            
            status = "✅ COMPLETE" if is_complete else "🔧 INCOMPLETE"
            print(f"{lesson['sequence_order']:2d}. {lesson['title']:<40} {status}")
            print(f"    Content: {vocab_count}v, {conv_count}c, {grammar_count}g, {exercise_count}e")
            
            if is_complete:
                complete_count += 1
            else:
                incomplete_count += 1
        
        print(f"\n📊 FINAL STATUS:")
        print(f"  • Complete Lessons: {complete_count}")
        print(f"  • Incomplete Lessons: {incomplete_count}")
        print(f"  • Completion Rate: {(complete_count/(complete_count+incomplete_count))*100:.1f}%")

def main():
    """Main function"""
    completer = RobustLessonCompleter()
    
    # Complete all incomplete lessons
    results = completer.complete_all_lessons()
    
    # Display results
    print(f"\n📊 COMPLETION RESULTS:")
    print(f"  • Total Lessons: {results.get('total_lessons', 0)}")
    print(f"  • Processed: {results.get('processed', 0)}")
    print(f"  • Successful: {results.get('successful', 0)}")
    print(f"  • Failed: {len(results.get('failed', []))}")
    
    if results.get('completed_lessons'):
        print(f"\n✅ COMPLETED LESSONS:")
        for lesson in results['completed_lessons']:
            print(f"  • {lesson}")
    
    if results.get('failed'):
        print(f"\n❌ FAILED LESSONS:")
        for failure in results['failed']:
            print(f"  • {failure}")
    
    # Run final validation
    completer.run_final_validation()
    
    print(f"\n📋 NEXT STEPS:")
    print("1. Generate audio for all completed lessons")
    print("2. Test lessons in iOS app")
    print("3. Run comprehensive quality validation")
    print("4. Verify romanized Tamil pronunciations")

if __name__ == "__main__":
    main()
