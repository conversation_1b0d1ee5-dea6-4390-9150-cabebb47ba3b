#!/usr/bin/env python3
"""
Systematic Grammar Validator for NIRA
Step 3: Validate and fix ONLY grammar points for all 30 lessons

This script:
1. Validates grammar points against quality checklist
2. Identifies placeholder/poor content
3. Generates new authentic Tamil grammar points
4. Updates only grammar section
5. Moves to next lesson

Following the systematic approach: Vocabulary ✅ → Conversations ✅ → Grammar → Exercises
"""

import json
import requests
import time
from typing import Dict, List, Any
import google.generativeai as genai

# API Configuration
GEMINI_API_KEY = "AIzaSyAYasSocME9nPr7HP625EEx4cofwm5MH3Q"
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# Initialize Gemini
genai.configure(api_key=GEMINI_API_KEY)
gemini_model = genai.GenerativeModel('gemini-2.0-flash-exp')

# Tamil A1 Path ID
TAMIL_A1_PATH_ID = "6b427613-420f-4586-bce8-2773d722f0b4"

class SystematicGrammarValidator:
    """Validates and fixes grammar points systematically"""
    
    def __init__(self):
        self.headers = {
            'apikey': SUPABASE_KEY,
            'Authorization': f'Bearer {SUPABASE_KEY}',
            'Content-Type': 'application/json'
        }
    
    def get_all_lessons(self) -> List[Dict[str, Any]]:
        """Get all Tamil A1 lessons"""
        print("🔍 Fetching all Tamil A1 lessons...")
        
        params = {
            'select': 'id,title,sequence_order,content_metadata',
            'path_id': f'eq.{TAMIL_A1_PATH_ID}',
            'order': 'sequence_order'
        }
        
        response = requests.get(f"{SUPABASE_URL}/rest/v1/lessons", headers=self.headers, params=params)
        
        if response.status_code == 200:
            lessons = response.json()
            print(f"✅ Found {len(lessons)} lessons")
            return lessons
        else:
            print(f"❌ Error fetching lessons: {response.status_code}")
            return []
    
    def validate_grammar_quality(self, grammar_points: List[Dict[str, Any]], lesson_title: str) -> Dict[str, Any]:
        """Validate grammar points against quality checklist"""
        issues = []
        
        # Check count
        if len(grammar_points) < 10:
            issues.append(f"Insufficient grammar points: {len(grammar_points)}/10")
        
        # Check for placeholder content
        placeholder_count = 0
        for grammar in grammar_points:
            rule = grammar.get('rule', '').lower()
            explanation = grammar.get('explanation', '').lower()
            if any(placeholder in rule for placeholder in ['grammar', 'rule', 'point']):
                placeholder_count += 1
        
        if placeholder_count > 3:  # Allow some generic rules
            issues.append(f"Generic grammar rules: {placeholder_count} items")
        
        # Check for topic relevance
        topic_words = lesson_title.lower().split()
        relevant_count = 0
        for grammar in grammar_points:
            examples = grammar.get('examples', [])
            for example in examples:
                if any(topic_word in example.lower() for topic_word in topic_words):
                    relevant_count += 1
                    break
        
        relevance_score = (relevant_count / len(grammar_points)) * 100 if grammar_points else 0
        
        # Check for missing examples
        missing_examples = 0
        for grammar in grammar_points:
            examples = grammar.get('examples', [])
            if len(examples) < 3:
                missing_examples += 1
        
        if missing_examples > 0:
            issues.append(f"Insufficient examples: {missing_examples} grammar points")
        
        # Check for duplicates
        rules = [grammar.get('rule', '') for grammar in grammar_points]
        duplicates = len(rules) - len(set(rules))
        if duplicates > 0:
            issues.append(f"Duplicate grammar rules: {duplicates} items")
        
        # Check for audio URLs
        missing_audio = 0
        for grammar in grammar_points:
            if not grammar.get('examples_audio_urls'):
                missing_audio += 1
        
        if missing_audio > 0:
            issues.append(f"Missing audio URLs: {missing_audio} grammar points")
        
        return {
            'total_items': len(grammar_points),
            'placeholder_count': placeholder_count,
            'relevance_score': relevance_score,
            'missing_examples': missing_examples,
            'duplicates': duplicates,
            'missing_audio': missing_audio,
            'issues': issues,
            'needs_regeneration': len(issues) > 0 or relevance_score < 50
        }
    
    def generate_authentic_grammar(self, lesson_title: str) -> List[Dict[str, Any]]:
        """Generate authentic Tamil grammar points for the lesson topic"""
        print(f"🔄 Generating authentic grammar points for: {lesson_title}")
        
        prompt = f"""
        Generate exactly 10 authentic Tamil grammar points for the A1 lesson: "{lesson_title}"

        Requirements:
        - Grammar points must be relevant to {lesson_title} context
        - Use authentic Chennai Tamil
        - A1 beginner level difficulty
        - Each point has clear explanation and 3+ examples
        - Examples should use vocabulary related to {lesson_title}
        - Include practical learning tips
        - No placeholder or generic content

        Return as valid JSON array:
        [
            {{
                "rule": "specific_grammar_rule_name",
                "explanation": "clear_explanation_of_the_rule",
                "examples": [
                    "tamil_example_1_related_to_{lesson_title.lower()}",
                    "tamil_example_2_related_to_{lesson_title.lower()}",
                    "tamil_example_3_related_to_{lesson_title.lower()}"
                ],
                "tips": "practical_learning_tip_for_this_rule",
                "difficulty": "beginner",
                "examples_audio_urls": [
                    "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/{lesson_title.lower().replace(' ', '_')}/grammar_{{grammar_index:02d}}_01.mp3",
                    "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/{lesson_title.lower().replace(' ', '_')}/grammar_{{grammar_index:02d}}_02.mp3",
                    "https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/{lesson_title.lower().replace(' ', '_')}/grammar_{{grammar_index:02d}}_03.mp3"
                ]
            }}
        ]

        Generate exactly 10 grammar points. Focus on practical grammar that helps with {lesson_title}.
        """
        
        try:
            response = gemini_model.generate_content(prompt)
            content = response.text
            
            # Clean and parse JSON
            if "```json" in content:
                content = content.split("```json")[1].split("```")[0]
            elif "```" in content:
                content = content.split("```")[1].split("```")[0]
            
            grammar_points = json.loads(content.strip())
            
            # Validate we got exactly 10 grammar points
            if len(grammar_points) != 10:
                print(f"⚠️ Generated {len(grammar_points)} grammar points, expected 10")
                # Truncate or pad as needed
                if len(grammar_points) > 10:
                    grammar_points = grammar_points[:10]
                elif len(grammar_points) < 10:
                    # Duplicate some points to reach 10
                    while len(grammar_points) < 10:
                        grammar_points.append(grammar_points[len(grammar_points) % len(grammar_points)])
            
            # Add proper audio URLs with correct indexing
            lesson_slug = lesson_title.lower().replace(' ', '_').replace('&', 'and')
            for grammar_idx, grammar in enumerate(grammar_points, 1):
                examples = grammar.get('examples', [])
                audio_urls = []
                for ex_idx in range(len(examples)):
                    audio_urls.append(f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/{lesson_slug}/grammar_{grammar_idx:02d}_{ex_idx+1:02d}.mp3")
                grammar['examples_audio_urls'] = audio_urls
            
            print(f"✅ Generated {len(grammar_points)} authentic grammar points")
            return grammar_points
            
        except Exception as e:
            print(f"❌ Failed to generate grammar points: {e}")
            return []
    
    def update_lesson_grammar(self, lesson_id: str, new_grammar: List[Dict[str, Any]], existing_metadata: Dict[str, Any]) -> bool:
        """Update only the grammar section of a lesson"""
        try:
            # Keep existing vocabulary, conversations, exercises - only update grammar
            updated_metadata = existing_metadata.copy()
            updated_metadata['grammar_points'] = new_grammar
            
            update_url = f"{SUPABASE_URL}/rest/v1/lessons"
            params = {'id': f'eq.{lesson_id}'}
            
            update_data = {
                'content_metadata': updated_metadata,
                'updated_at': 'now()'
            }
            
            response = requests.patch(update_url, json=update_data, headers=self.headers, params=params)
            
            if response.status_code in [200, 204]:
                print(f"✅ Grammar points updated successfully")
                return True
            else:
                print(f"❌ Database update failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Error updating grammar points: {e}")
            return False
    
    def process_all_grammar(self) -> Dict[str, Any]:
        """Process grammar points for all 30 lessons systematically"""
        print("🚀 SYSTEMATIC GRAMMAR VALIDATOR")
        print("Step 3: Validating and fixing grammar points for all 30 lessons")
        print("=" * 70)
        
        lessons = self.get_all_lessons()
        if not lessons:
            return {}
        
        results = {
            'total_lessons': len(lessons),
            'processed': 0,
            'grammar_fixed': 0,
            'grammar_good': 0,
            'failed': [],
            'lesson_details': []
        }
        
        for i, lesson in enumerate(lessons, 1):
            print(f"\n📖 Processing {i}/{len(lessons)}: {lesson['title']}")
            
            metadata = lesson.get('content_metadata', {})
            grammar_points = metadata.get('grammar_points', [])
            
            # Validate current grammar points
            validation = self.validate_grammar_quality(grammar_points, lesson['title'])
            
            print(f"📊 Current grammar points: {validation['total_items']} items")
            print(f"📊 Issues: {', '.join(validation['issues']) if validation['issues'] else 'None'}")
            print(f"📊 Relevance score: {validation['relevance_score']:.1f}%")
            
            if validation['needs_regeneration']:
                print(f"🔄 Regenerating grammar points...")
                
                # Generate new grammar points
                new_grammar = self.generate_authentic_grammar(lesson['title'])
                
                if new_grammar:
                    # Update lesson
                    if self.update_lesson_grammar(lesson['id'], new_grammar, metadata):
                        results['grammar_fixed'] += 1
                        print(f"✅ {lesson['title']} grammar points fixed")
                    else:
                        results['failed'].append(lesson['title'])
                        print(f"❌ {lesson['title']} grammar update failed")
                else:
                    results['failed'].append(lesson['title'])
                    print(f"❌ {lesson['title']} grammar generation failed")
            else:
                results['grammar_good'] += 1
                print(f"✅ {lesson['title']} grammar points are good")
            
            results['processed'] += 1
            results['lesson_details'].append({
                'title': lesson['title'],
                'validation': validation,
                'action': 'fixed' if validation['needs_regeneration'] else 'good'
            })
            
            # Rate limiting
            time.sleep(2)
        
        return results

def main():
    """Main function"""
    print("🎯 SYSTEMATIC GRAMMAR VALIDATOR")
    print("Step 3 of 4: Grammar validation and generation")
    print("=" * 60)
    
    validator = SystematicGrammarValidator()
    
    # Process all grammar points
    results = validator.process_all_grammar()
    
    # Display results
    print(f"\n📊 GRAMMAR PROCESSING RESULTS:")
    print(f"  • Total Lessons: {results.get('total_lessons', 0)}")
    print(f"  • Processed: {results.get('processed', 0)}")
    print(f"  • Grammar Fixed: {results.get('grammar_fixed', 0)}")
    print(f"  • Grammar Good: {results.get('grammar_good', 0)}")
    print(f"  • Failed: {len(results.get('failed', []))}")
    
    if results.get('failed'):
        print(f"\n❌ FAILED LESSONS:")
        for failure in results['failed']:
            print(f"  • {failure}")
    
    print(f"\n📋 NEXT STEPS:")
    print("1. ✅ Vocabulary validation complete")
    print("2. ✅ Conversation validation complete")
    print("3. ✅ Grammar validation complete")
    print("4. 🔄 Finally: Run exercises validator")
    print("5. 🎵 Generate audio for all content")

if __name__ == "__main__":
    main()
