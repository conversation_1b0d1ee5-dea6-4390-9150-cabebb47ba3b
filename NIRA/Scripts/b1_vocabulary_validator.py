#!/usr/bin/env python3
"""
B1 Vocabulary Validator for NIRA
B1 Intermediate level Tamil lessons

This script creates and validates B1 intermediate level vocabulary:
- 35 vocabulary items per lesson
- Intermediate complexity vocabulary
- Complex grammar integration
- Intermediate level difficulty
"""

import json
import requests
import time
from typing import Dict, List, Any
import google.generativeai as genai

# API Configuration
GEMINI_API_KEY = "AIzaSyAYasSocME9nPr7HP625EEx4cofwm5MH3Q"
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# Initialize Gemini
genai.configure(api_key=GEMINI_API_KEY)
gemini_model = genai.GenerativeModel('gemini-2.0-flash-exp')

# Tamil B1 Path ID (will be created)
TAMIL_B1_PATH_ID = "b1-intermediate-path-id"

# B1 Intermediate Tamil Lessons (30 lessons)
B1_LESSONS = [
    "Complex Social Situations and Etiquette",
    "Professional Networking and Business Relations",
    "Advanced Mathematical and Scientific Concepts",
    "Detailed Physical and Emotional Descriptions",
    "Fine Dining and Culinary Traditions",
    "Comprehensive Healthcare and Medical Procedures",
    "Climate Change and Environmental Issues",
    "Advanced Transportation and Logistics",
    "Fashion Industry and Design Concepts",
    "Home Management and Domestic Economics",
    "Biographical Narratives and Life Stories",
    "Real Estate and Property Management",
    "Project Management and Productivity",
    "Investment and Financial Planning",
    "Urban Planning and City Development",
    "Emergency Response and Crisis Management",
    "Entertainment Industry and Media",
    "Professional Development and Career Advancement",
    "Higher Education and Research Methods",
    "Advanced Technology and Digital Innovation",
    "Political Discourse and Social Commentary",
    "Cultural Heritage and Traditional Arts",
    "Sustainability and Green Living",
    "Advanced Cooking Techniques and Gastronomy",
    "Historical Analysis and Future Predictions",
    "Metropolitan Transportation Systems",
    "International Business and Global Trade",
    "Contemporary Art and Cultural Movements",
    "Archaeological Sites and Cultural Preservation",
    "Competitive Sports and Athletic Performance"
]

class B1VocabularyValidator:
    """B1 vocabulary validator and creator"""
    
    def __init__(self):
        self.headers = {
            'apikey': SUPABASE_KEY,
            'Authorization': f'Bearer {SUPABASE_KEY}',
            'Content-Type': 'application/json'
        }
    
    def create_b1_lesson_with_vocabulary(self, title: str, sequence: int) -> bool:
        """Create B1 lesson with vocabulary directly"""
        print(f"\n🔧 CREATING B1 LESSON: {title}")
        
        # Generate B1 vocabulary
        vocabulary = self.generate_b1_vocabulary(title)
        
        if not vocabulary:
            print(f"❌ Failed to generate vocabulary for {title}")
            return False
        
        # Create lesson data
        lesson_data = {
            'path_id': TAMIL_B1_PATH_ID,
            'title': title,
            'description': f'Intermediate level Tamil lesson covering {title.lower()} with complex vocabulary and advanced grammar',
            'lesson_type': 'comprehensive',
            'difficulty_level': 3,  # B1 level
            'estimated_duration': 60,
            'sequence_order': sequence,
            'learning_objectives': [
                f'Master intermediate vocabulary related to {title.lower()}',
                f'Engage in complex discussions about {title.lower()}',
                f'Apply intermediate grammar rules in {title.lower()} context',
                f'Analyze and evaluate {title.lower()} concepts'
            ],
            'vocabulary_focus': None,
            'grammar_concepts': [],
            'cultural_notes': '',
            'prerequisite_lessons': [],
            'content_metadata': {
                'title': title,
                'description': f'Intermediate level Tamil lesson covering {title.lower()}',
                'vocabulary': vocabulary,
                'conversations': [],
                'grammar_points': [],
                'exercises': [],
                'estimated_duration': 60,
                'learning_objectives': [
                    f'Master intermediate vocabulary related to {title.lower()}',
                    f'Engage in complex discussions about {title.lower()}',
                    f'Apply intermediate grammar rules in {title.lower()} context',
                    f'Analyze and evaluate {title.lower()} concepts'
                ]
            },
            'is_active': True,
            'audio_url': None,
            'audio_metadata': {},
            'has_audio': False
        }
        
        try:
            response = requests.post(f"{SUPABASE_URL}/rest/v1/lessons", json=lesson_data, headers=self.headers)
            
            if response.status_code == 201:
                print(f"✅ Created B1 lesson: {title}")
                print(f"📊 Generated: {len(vocabulary)} vocabulary items")
                return True
            else:
                print(f"❌ Database insert failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Error creating lesson: {e}")
            return False
    
    def generate_b1_vocabulary(self, lesson_title: str) -> List[Dict[str, Any]]:
        """Generate B1 level Tamil vocabulary"""
        print(f"🔄 Generating B1 vocabulary for: {lesson_title}")
        
        prompt = f"""
        Generate exactly 35 authentic Tamil vocabulary items for the B1 intermediate lesson: "{lesson_title}"

        Requirements:
        - B1 intermediate level difficulty
        - Complex vocabulary with nuanced meanings
        - Technical terms related to {lesson_title}
        - Abstract concepts and sophisticated expressions
        - Use authentic Chennai Tamil
        - Include idiomatic expressions
        - Each word must be unique

        Return as valid JSON array with 35 items for intermediate level complexity.
        """
        
        try:
            response = gemini_model.generate_content(prompt)
            content = response.text
            
            if "```json" in content:
                content = content.split("```json")[1].split("```")[0]
            elif "```" in content:
                content = content.split("```")[1].split("```")[0]
            
            vocabulary = json.loads(content.strip())
            
            if len(vocabulary) != 35:
                vocabulary = vocabulary[:35] if len(vocabulary) > 35 else vocabulary + vocabulary[:35-len(vocabulary)]
            
            lesson_slug = lesson_title.lower().replace(' ', '_').replace('&', 'and')
            for i, vocab in enumerate(vocabulary, 1):
                vocab['word_audio_url'] = f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/b1/{lesson_slug}/vocab_{i:02d}_word.mp3"
                vocab['example_audio_url'] = f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/b1/{lesson_slug}/vocab_{i:02d}_example.mp3"
            
            print(f"✅ Generated {len(vocabulary)} B1 vocabulary items")
            return vocabulary
            
        except Exception as e:
            print(f"❌ Failed to generate vocabulary: {e}")
            return []
    
    def create_all_b1_lessons(self) -> Dict[str, Any]:
        """Create all 30 B1 lessons with vocabulary"""
        print("🚀 B1 INTERMEDIATE VOCABULARY VALIDATOR")
        print("Creating 30 intermediate Tamil lessons")
        print("=" * 60)
        
        results = {'total_lessons': len(B1_LESSONS), 'created': 0, 'failed': []}
        
        for i, lesson_title in enumerate(B1_LESSONS, 1):
            print(f"\n📖 Processing {i}/{len(B1_LESSONS)}: {lesson_title}")
            
            if self.create_b1_lesson_with_vocabulary(lesson_title, i):
                results['created'] += 1
            else:
                results['failed'].append(lesson_title)
            
            time.sleep(2)
        
        return results

def main():
    validator = B1VocabularyValidator()
    results = validator.create_all_b1_lessons()
    
    print(f"\n📊 B1 RESULTS: Created {results['created']}/{results['total_lessons']} lessons")
    if results['failed']:
        print(f"❌ Failed: {len(results['failed'])} lessons")

if __name__ == "__main__":
    main()
