#!/usr/bin/env python3
"""
Main Orchestrator - Process all 30 A1 Tamil lessons automatically
Uses chunked approach: Gemini generation + dual validation + database update
"""

import sys
import time
import json
from typing import List, Dict, Any

# Import our modules
from gemini_content_generator import GeminiContentGenerator
from quality_validator import QualityValidator
import requests

class LessonOrchestrator:
    def __init__(self):
        # API Keys - Replace with actual keys
        self.gemini_key = "AIzaSyBVZ_5-2Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8"
        self.claude_key = "sk-ant-api03-..."
        self.openai_key = "sk-proj-..."
        
        # Initialize components
        self.content_generator = GeminiContentGenerator(self.gemini_key)
        self.quality_validator = QualityValidator(self.claude_key, self.openai_key)
        
        # Supabase config
        self.supabase_url = "https://lyaojebttnqilmdosmjk.supabase.co"
        self.supabase_key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"
        
        # Lessons to process (remaining 26 after 4 completed)
        self.remaining_lessons = [
            "Clothing and Shopping",
            "Colors and Description", 
            "Common Verbs and Actions",
            "Daily Routines and Activities",
            "Education and Learning",
            "Emotions and Feelings",
            "Family and Relationships",
            "Food and Cooking",
            "Hobbies and Interests",
            "House and Home",
            "Music and Movies",
            "Numbers and Counting",
            "Occupations and Work",
            "Sports and Exercise",
            "Technology and Communication",
            "Time and Calendar",
            "Transportation and Travel",
            "Weather and Seasons",
            "Festivals and Celebrations",
            "Famous Landmarks",
            "Vegetables",
            "Days Weeks Months",
            "Local Transportation",
            "Travel and Tourism",
            "Basic Shopping",
            "Cultural Traditions"
        ]
        
        # Progress tracking
        self.completed_lessons = []
        self.failed_lessons = []
        self.current_lesson = 0
        
    def get_lesson_id(self, lesson_title: str) -> str:
        """Get lesson ID from Supabase"""
        url = f"{self.supabase_url}/rest/v1/lessons?title=eq.{lesson_title}&select=id"
        headers = {
            "apikey": self.supabase_key,
            "Authorization": f"Bearer {self.supabase_key}"
        }
        
        try:
            response = requests.get(url, headers=headers)
            if response.status_code == 200 and response.json():
                return response.json()[0]['id']
        except Exception as e:
            print(f"❌ Error getting lesson ID for {lesson_title}: {e}")
        
        return None
    
    def update_lesson_in_database(self, lesson_id: str, content: Dict[str, Any]) -> bool:
        """Update lesson in Supabase"""
        url = f"{self.supabase_url}/rest/v1/lessons?id=eq.{lesson_id}"
        headers = {
            "apikey": self.supabase_key,
            "Authorization": f"Bearer {self.supabase_key}",
            "Content-Type": "application/json",
            "Prefer": "return=minimal"
        }
        
        try:
            update_data = {"content_metadata": content}
            response = requests.patch(url, headers=headers, json=update_data)
            return response.status_code == 204
        except Exception as e:
            print(f"❌ Database update error: {e}")
            return False
    
    def process_single_lesson(self, lesson_title: str) -> bool:
        """Process a single lesson through the complete pipeline"""
        
        print(f"\n{'='*60}")
        print(f"🎯 PROCESSING: {lesson_title}")
        print(f"📊 Progress: {self.current_lesson + 1}/{len(self.remaining_lessons)} lessons")
        print(f"{'='*60}")
        
        # Step 1: Get lesson ID
        print("📋 Step 1: Getting lesson ID...")
        lesson_id = self.get_lesson_id(lesson_title)
        if not lesson_id:
            print(f"❌ Lesson not found in database: {lesson_title}")
            return False
        print(f"✅ Found lesson ID: {lesson_id}")
        
        # Step 2: Generate content with Gemini
        print("🤖 Step 2: Generating content with Gemini Flash 2.0...")
        try:
            content = self.content_generator.generate_lesson_content(lesson_title)
            print(f"✅ Generated content:")
            print(f"   - Vocabulary: {len(content.get('vocabulary', []))} items")
            print(f"   - Conversations: {len(content.get('conversations', []))} scenarios")
            print(f"   - Grammar: {len(content.get('grammar_points', []))} points")
            print(f"   - Exercises: {len(content.get('exercises', []))} questions")
        except Exception as e:
            print(f"❌ Content generation failed: {e}")
            return False
        
        # Step 3: Quick validation
        print("⚡ Step 3: Quick validation...")
        quick_check = self.quality_validator.quick_validation(content)
        if not all(quick_check.values()):
            print(f"❌ Quick validation failed: {quick_check}")
            return False
        print("✅ Quick validation passed")
        
        # Step 4: Comprehensive validation
        print("🔍 Step 4: Comprehensive validation (Claude + GPT-4)...")
        try:
            validation_result = self.quality_validator.comprehensive_validation(content, lesson_title)
            
            if validation_result['passed_validation']:
                print(f"✅ Validation passed (Score: {validation_result['consensus_score']:.1f}/100)")
            else:
                print(f"❌ Validation failed (Score: {validation_result['consensus_score']:.1f}/100)")
                print(f"Issues: {validation_result['critical_issues']}")
                return False
                
        except Exception as e:
            print(f"❌ Validation failed: {e}")
            return False
        
        # Step 5: Update database
        print("💾 Step 5: Updating database...")
        try:
            success = self.update_lesson_in_database(lesson_id, content)
            if success:
                print("✅ Database updated successfully")
            else:
                print("❌ Database update failed")
                return False
        except Exception as e:
            print(f"❌ Database update error: {e}")
            return False
        
        print(f"🎉 {lesson_title} COMPLETED SUCCESSFULLY!")
        return True
    
    def run_all_lessons(self):
        """Run the complete pipeline for all remaining lessons"""
        
        print("🚀 STARTING AUTOMATED LESSON PIPELINE")
        print(f"📚 Processing {len(self.remaining_lessons)} A1 Tamil lessons")
        print(f"🔧 Pipeline: Gemini → Claude → GPT-4 → Database")
        print("="*60)
        
        start_time = time.time()
        
        for i, lesson_title in enumerate(self.remaining_lessons):
            self.current_lesson = i
            
            try:
                success = self.process_single_lesson(lesson_title)
                
                if success:
                    self.completed_lessons.append(lesson_title)
                    print(f"✅ SUCCESS: {lesson_title}")
                else:
                    self.failed_lessons.append(lesson_title)
                    print(f"❌ FAILED: {lesson_title}")
                
                # Progress update
                completed_count = len(self.completed_lessons)
                total_count = len(self.remaining_lessons)
                progress_percent = (completed_count / total_count) * 100
                
                print(f"\n📊 PROGRESS UPDATE:")
                print(f"   ✅ Completed: {completed_count}/{total_count} ({progress_percent:.1f}%)")
                print(f"   ❌ Failed: {len(self.failed_lessons)}")
                print(f"   ⏱️ Time elapsed: {(time.time() - start_time)/60:.1f} minutes")
                
                # Brief pause between lessons
                if i < len(self.remaining_lessons) - 1:
                    print("⏸️ Pausing 3 seconds before next lesson...")
                    time.sleep(3)
                    
            except KeyboardInterrupt:
                print("\n⏹️ Pipeline stopped by user")
                break
            except Exception as e:
                print(f"❌ Unexpected error processing {lesson_title}: {e}")
                self.failed_lessons.append(lesson_title)
                continue
        
        # Final report
        self._print_final_report(start_time)
    
    def _print_final_report(self, start_time: float):
        """Print final pipeline report"""
        
        total_time = (time.time() - start_time) / 60
        completed_count = len(self.completed_lessons)
        failed_count = len(self.failed_lessons)
        total_count = len(self.remaining_lessons)
        success_rate = (completed_count / total_count) * 100
        
        print("\n" + "="*60)
        print("🎉 PIPELINE COMPLETED!")
        print("="*60)
        print(f"📊 FINAL RESULTS:")
        print(f"   ✅ Completed: {completed_count}/{total_count} lessons ({success_rate:.1f}%)")
        print(f"   ❌ Failed: {failed_count} lessons")
        print(f"   ⏱️ Total time: {total_time:.1f} minutes")
        print(f"   ⚡ Average: {total_time/total_count:.1f} minutes per lesson")
        
        if self.completed_lessons:
            print(f"\n✅ COMPLETED LESSONS:")
            for lesson in self.completed_lessons:
                print(f"   - {lesson}")
        
        if self.failed_lessons:
            print(f"\n❌ FAILED LESSONS:")
            for lesson in self.failed_lessons:
                print(f"   - {lesson}")
            print(f"\n💡 Retry failed lessons individually for debugging")
        
        print(f"\n🎯 NEXT STEPS:")
        print(f"   1. Generate audio for {completed_count} completed lessons")
        print(f"   2. Test iOS app with new content")
        print(f"   3. Scale to other languages (49 remaining)")
        print("="*60)

if __name__ == "__main__":
    orchestrator = LessonOrchestrator()
    orchestrator.run_all_lessons()
