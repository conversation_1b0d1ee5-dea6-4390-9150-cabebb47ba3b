#!/usr/bin/env python3
"""
Scalable Lesson Updater for NIRA
Following Complete_Lesson_Implementation_Guide.md and COMPREHENSIVE_QUALITY_CHECKLIST.md

This script systematically updates lessons in the specified order:
1. Colors and Descriptions
2. Days, Weeks, and Months  
3. Daily Routines and Activities
4. Clothing and Shopping
5. Common Verbs and Actions

Each lesson will be processed with multiple quality verifications before completion.
"""

import os
import sys
import json
import requests
import time
from typing import Dict, List, Any, Optional
import subprocess

# Add the Scripts directory to Python path
sys.path.append('/Users/<USER>/Documents/NIRA/NIRA/Scripts')

# Import existing modules
try:
    from gemini_content_generator import GeminiContentGenerator
    from quality_validator import QualityValidator
except ImportError as e:
    print(f"⚠️  Warning: Could not import modules: {e}")
    print("Will create basic implementations")

# Supabase configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# Tamil A1 Path ID
TAMIL_A1_PATH_ID = "6b427613-420f-4586-bce8-2773d722f0b4"

# Priority lessons to update (in order)
PRIORITY_LESSONS = [
    "Colors and Descriptions",
    "Days, Weeks, and Months", 
    "Daily Routines and Activities",
    "Clothing and Shopping",
    "Common Verbs and Actions"
]

class ScalableLessonUpdater:
    """Scalable lesson updater following the implementation guide"""
    
    def __init__(self):
        self.headers = {
            'apikey': SUPABASE_KEY,
            'Authorization': f'Bearer {SUPABASE_KEY}',
            'Content-Type': 'application/json'
        }
        self.quality_checklist = self._load_quality_checklist()
        
    def _load_quality_checklist(self) -> List[str]:
        """Load quality checklist items from documentation"""
        return [
            "All 25 vocabulary items are unique and topic-specific",
            "All 15 conversations are unique (no repeated content)",
            "All 10 grammar points are unique and relevant", 
            "All 5+ exercises are unique with varied question types",
            "No placeholder content or English examples in Tamil lessons",
            "Conversations show green romanized Tamil below each line",
            "Exercise options show romanized Tamil in green brackets",
            "All pronunciation data matches corresponding Tamil text",
            "Content reflects authentic Tamil culture",
            "A1 difficulty level maintained throughout"
        ]
    
    def get_tamil_lessons(self) -> List[Dict[str, Any]]:
        """Get all Tamil A1 lessons from database"""
        print("🔍 Fetching Tamil A1 lessons...")

        params = {
            'select': 'id,title,sequence_order,content_metadata,has_audio',
            'path_id': f'eq.{TAMIL_A1_PATH_ID}',
            'order': 'sequence_order'
        }

        response = requests.get(f"{SUPABASE_URL}/rest/v1/lessons", headers=self.headers, params=params)

        if response.status_code != 200:
            print(f"❌ Error fetching lessons: {response.status_code}")
            print(f"Response: {response.text}")
            return []

        lessons = response.json()
        print(f"✅ Found {len(lessons)} Tamil A1 lessons")
        return lessons
    
    def analyze_lesson_quality(self, lesson: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze lesson quality against comprehensive checklist"""
        metadata = lesson.get('content_metadata', {})
        
        analysis = {
            'lesson_id': lesson['id'],
            'title': lesson['title'],
            'vocabulary_count': len(metadata.get('vocabulary', [])),
            'conversations_count': len(metadata.get('conversations', [])),
            'grammar_count': len(metadata.get('grammar_points', [])),
            'exercises_count': len(metadata.get('exercises', [])),
            'has_pronunciations': False,
            'has_audio_urls': False,
            'content_quality_score': 0,
            'issues': []
        }
        
        # Check content completeness
        if analysis['vocabulary_count'] < 25:
            analysis['issues'].append(f"Vocabulary incomplete: {analysis['vocabulary_count']}/25")
        if analysis['conversations_count'] < 15:
            analysis['issues'].append(f"Conversations incomplete: {analysis['conversations_count']}/15")
        if analysis['grammar_count'] < 10:
            analysis['issues'].append(f"Grammar incomplete: {analysis['grammar_count']}/10")
        if analysis['exercises_count'] < 5:
            analysis['issues'].append(f"Exercises incomplete: {analysis['exercises_count']}/5")
            
        # Check for pronunciations
        conversations = metadata.get('conversations', [])
        if conversations and any('pronunciation' in conv for conv in conversations):
            analysis['has_pronunciations'] = True
            
        # Check for audio URLs
        vocabulary = metadata.get('vocabulary', [])
        if vocabulary and any('word_audio_url' in vocab for vocab in vocabulary):
            analysis['has_audio_urls'] = True
            
        # Calculate quality score
        max_score = 100
        score = 0
        
        if analysis['vocabulary_count'] >= 25: score += 25
        if analysis['conversations_count'] >= 15: score += 25  
        if analysis['grammar_count'] >= 10: score += 25
        if analysis['exercises_count'] >= 5: score += 15
        if analysis['has_pronunciations']: score += 5
        if analysis['has_audio_urls']: score += 5
        
        analysis['content_quality_score'] = score
        analysis['is_complete'] = score >= 90 and len(analysis['issues']) == 0
        
        return analysis
    
    def find_priority_lessons(self, lessons: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Find priority lessons that need updating"""
        priority_lessons = []

        for lesson in lessons:
            title = lesson['title']
            for priority_title in PRIORITY_LESSONS:
                if priority_title.lower() in title.lower() or any(word in title.lower() for word in priority_title.lower().split()):
                    priority_lessons.append(lesson)
                    break

        return priority_lessons

    def get_incomplete_lessons(self, lessons: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Get lessons that need completion based on quality analysis"""
        incomplete_lessons = []

        for lesson in lessons:
            analysis = self.analyze_lesson_quality(lesson)
            if not analysis['is_complete']:
                incomplete_lessons.append({
                    'lesson': lesson,
                    'analysis': analysis
                })

        # Sort by priority (lowest quality score first)
        incomplete_lessons.sort(key=lambda x: x['analysis']['content_quality_score'])

        return incomplete_lessons

    def validate_lesson_content(self, lesson: Dict[str, Any]) -> Dict[str, bool]:
        """Validate lesson content against comprehensive quality checklist"""
        metadata = lesson.get('content_metadata', {})
        validation = {}

        # Content completeness validation
        validation['has_25_vocabulary'] = len(metadata.get('vocabulary', [])) >= 25
        validation['has_15_conversations'] = len(metadata.get('conversations', [])) >= 15
        validation['has_10_grammar'] = len(metadata.get('grammar_points', [])) >= 10
        validation['has_5_exercises'] = len(metadata.get('exercises', [])) >= 5

        # Content quality validation
        vocabulary = metadata.get('vocabulary', [])
        conversations = metadata.get('conversations', [])
        exercises = metadata.get('exercises', [])

        # Check for pronunciations
        validation['conversations_have_pronunciations'] = all(
            'pronunciation' in conv for conv in conversations
        ) if conversations else False

        validation['exercises_have_pronunciations'] = all(
            'options_pronunciations' in ex for ex in exercises
        ) if exercises else False

        # Check for audio URLs
        validation['vocabulary_has_audio'] = all(
            'word_audio_url' in vocab and 'example_audio_url' in vocab
            for vocab in vocabulary
        ) if vocabulary else False

        validation['conversations_have_audio'] = all(
            'audio_url' in conv for conv in conversations
        ) if conversations else False

        # Check for placeholder content
        validation['no_placeholder_content'] = True
        for vocab in vocabulary:
            if 'placeholder' in str(vocab).lower() or 'example' in vocab.get('word', '').lower():
                validation['no_placeholder_content'] = False
                break

        return validation
    
    def display_lesson_status(self, lessons: List[Dict[str, Any]]) -> None:
        """Display current status of all lessons"""
        print("\n📊 CURRENT LESSON STATUS")
        print("=" * 80)
        
        for lesson in lessons:
            analysis = self.analyze_lesson_quality(lesson)
            status = "✅ COMPLETE" if analysis['is_complete'] else "🔧 NEEDS UPDATE"
            
            print(f"{lesson['sequence_order']:2d}. {lesson['title']:<40} {status}")
            print(f"    Content: {analysis['vocabulary_count']}v, {analysis['conversations_count']}c, {analysis['grammar_count']}g, {analysis['exercises_count']}e")
            print(f"    Quality Score: {analysis['content_quality_score']}/100")
            
            if analysis['issues']:
                print(f"    Issues: {', '.join(analysis['issues'])}")
            print()

    def process_incomplete_lesson(self, lesson_data: Dict[str, Any]) -> bool:
        """Process a single incomplete lesson following the implementation guide"""
        lesson = lesson_data['lesson']
        analysis = lesson_data['analysis']

        print(f"\n🔧 PROCESSING: {lesson['title']}")
        print(f"Current Score: {analysis['content_quality_score']}/100")
        print(f"Issues: {', '.join(analysis['issues'])}")

        # Use existing comprehensive lesson generator
        try:
            script_path = "/Users/<USER>/Documents/NIRA/NIRA/Scripts/comprehensive_lesson_generator.py"
            lesson_title = lesson['title']
            lesson_id = lesson['id']

            # Run the comprehensive lesson generator for this specific lesson
            cmd = f"cd /Users/<USER>/Documents/NIRA/NIRA/Scripts && python3 -c \"" \
                  f"from comprehensive_lesson_generator import ComprehensiveLessonGenerator; " \
                  f"generator = ComprehensiveLessonGenerator(); " \
                  f"lesson_data = generator.generate_comprehensive_lesson_content('{lesson_title}', {lesson['sequence_order']}); " \
                  f"generator.update_lesson_in_database(lesson_data)\""

            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=300)

            if result.returncode == 0:
                print(f"✅ Successfully processed {lesson['title']}")
                return True
            else:
                print(f"❌ Failed to process {lesson['title']}: {result.stderr}")
                return False

        except subprocess.TimeoutExpired:
            print(f"⏰ Timeout processing {lesson['title']}")
            return False
        except Exception as e:
            print(f"❌ Error processing {lesson['title']}: {e}")
            return False

    def run_quality_validation(self, lesson: Dict[str, Any]) -> bool:
        """Run comprehensive quality validation on a lesson"""
        print(f"\n🔍 QUALITY VALIDATION: {lesson['title']}")

        validation = self.validate_lesson_content(lesson)

        print("📋 Quality Checklist:")
        for check, passed in validation.items():
            status = "✅" if passed else "❌"
            print(f"  {status} {check.replace('_', ' ').title()}")

        # Calculate pass rate
        total_checks = len(validation)
        passed_checks = sum(validation.values())
        pass_rate = (passed_checks / total_checks) * 100

        print(f"\n📊 Quality Score: {passed_checks}/{total_checks} ({pass_rate:.1f}%)")

        return pass_rate >= 90  # 90% pass rate required

    def process_priority_lessons(self, lessons: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Process priority lessons systematically"""
        print("\n🚀 PROCESSING PRIORITY LESSONS")
        print("=" * 50)

        # Get incomplete lessons
        incomplete_lessons = self.get_incomplete_lessons(lessons)

        if not incomplete_lessons:
            print("🎉 All lessons are already complete!")
            return {'status': 'all_complete', 'processed': 0, 'successful': 0}

        print(f"📋 Found {len(incomplete_lessons)} lessons needing updates")

        results = {
            'status': 'processing',
            'processed': 0,
            'successful': 0,
            'failed': [],
            'completed_lessons': []
        }

        # Process each incomplete lesson
        for i, lesson_data in enumerate(incomplete_lessons[:5], 1):  # Process first 5
            lesson = lesson_data['lesson']
            print(f"\n📖 Processing {i}/{min(5, len(incomplete_lessons))}: {lesson['title']}")

            # Process the lesson
            if self.process_incomplete_lesson(lesson_data):
                # Re-fetch and validate the lesson
                updated_lessons = self.get_tamil_lessons()
                updated_lesson = next((l for l in updated_lessons if l['id'] == lesson['id']), None)

                if updated_lesson and self.run_quality_validation(updated_lesson):
                    results['successful'] += 1
                    results['completed_lessons'].append(lesson['title'])
                    print(f"✅ {lesson['title']} completed successfully")
                else:
                    results['failed'].append(f"{lesson['title']}: Quality validation failed")
                    print(f"⚠️ {lesson['title']} processed but quality validation failed")
            else:
                results['failed'].append(f"{lesson['title']}: Processing failed")
                print(f"❌ {lesson['title']} processing failed")

            results['processed'] += 1

            # Rate limiting between lessons
            time.sleep(3)

        return results

def main():
    """Main function to run scalable lesson updates"""
    print("🎯 SCALABLE LESSON UPDATER FOR NIRA")
    print("Following Complete_Lesson_Implementation_Guide.md")
    print("Following COMPREHENSIVE_QUALITY_CHECKLIST.md")
    print("=" * 70)

    updater = ScalableLessonUpdater()

    # Get all Tamil lessons
    lessons = updater.get_tamil_lessons()
    if not lessons:
        print("❌ No lessons found. Exiting.")
        return

    # Display current status
    updater.display_lesson_status(lessons)

    # Find priority lessons
    priority_lessons = updater.find_priority_lessons(lessons)

    print(f"\n🎯 PRIORITY LESSONS IDENTIFIED: {len(priority_lessons)}")
    for lesson in priority_lessons:
        analysis = updater.analyze_lesson_quality(lesson)
        print(f"  • {lesson['title']} (Score: {analysis['content_quality_score']}/100)")

    # Process incomplete lessons
    print(f"\n🚀 STARTING SYSTEMATIC LESSON PROCESSING")
    results = updater.process_priority_lessons(lessons)

    # Display final results
    print(f"\n📊 PROCESSING RESULTS:")
    print(f"  • Processed: {results['processed']}")
    print(f"  • Successful: {results['successful']}")
    print(f"  • Failed: {len(results['failed'])}")

    if results['completed_lessons']:
        print(f"\n✅ COMPLETED LESSONS:")
        for lesson_title in results['completed_lessons']:
            print(f"  • {lesson_title}")

    if results['failed']:
        print(f"\n❌ FAILED LESSONS:")
        for failure in results['failed']:
            print(f"  • {failure}")

    print(f"\n📋 NEXT STEPS:")
    print("1. Review completed lessons in iOS app")
    print("2. Generate audio for completed lessons")
    print("3. Run comprehensive quality validation")
    print("4. Process remaining incomplete lessons")

if __name__ == "__main__":
    main()
