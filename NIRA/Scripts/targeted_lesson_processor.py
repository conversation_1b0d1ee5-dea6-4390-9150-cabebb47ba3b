#!/usr/bin/env python3
"""
Targeted Lesson Processor for NIRA
Specifically processes incomplete lessons following the quality checklist

This script focuses on the lessons that need completion:
- Family Members and Relationships
- Technology and Communication  
- Festivals and Celebrations
- Travel and Long Distance
- Music and Movies
- Famous Landmarks
- Sports and Games
"""

import os
import sys
import json
import requests
import time
from typing import Dict, List, Any
import subprocess

# Supabase configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# Tamil A1 Path ID
TAMIL_A1_PATH_ID = "6b427613-420f-4586-bce8-2773d722f0b4"

# Lessons that need completion (based on the status output)
INCOMPLETE_LESSONS = [
    "Family Members and Relationships",
    "Technology and Communication",
    "Festivals and Celebrations", 
    "Travel and Long Distance",
    "Music and Movies",
    "Famous Landmarks",
    "Sports and Games"
]

class TargetedLessonProcessor:
    """Targeted processor for incomplete lessons"""
    
    def __init__(self):
        self.headers = {
            'apikey': SUPABASE_KEY,
            'Authorization': f'Bearer {SUPABASE_KEY}',
            'Content-Type': 'application/json'
        }
    
    def get_incomplete_lessons(self) -> List[Dict[str, Any]]:
        """Get the specific incomplete lessons"""
        print("🔍 Fetching incomplete Tamil A1 lessons...")
        
        params = {
            'select': 'id,title,sequence_order,content_metadata',
            'path_id': f'eq.{TAMIL_A1_PATH_ID}',
            'order': 'sequence_order'
        }
        
        response = requests.get(f"{SUPABASE_URL}/rest/v1/lessons", headers=self.headers, params=params)
        
        if response.status_code != 200:
            print(f"❌ Error fetching lessons: {response.status_code}")
            return []
        
        all_lessons = response.json()
        
        # Filter for incomplete lessons
        incomplete_lessons = []
        for lesson in all_lessons:
            if any(incomplete_title in lesson['title'] for incomplete_title in INCOMPLETE_LESSONS):
                metadata = lesson.get('content_metadata', {})
                conv_count = len(metadata.get('conversations', []))
                grammar_count = len(metadata.get('grammar_points', []))
                exercise_count = len(metadata.get('exercises', []))
                
                # Check if actually incomplete
                if conv_count < 15 or grammar_count < 10 or exercise_count < 5:
                    incomplete_lessons.append(lesson)
        
        print(f"✅ Found {len(incomplete_lessons)} incomplete lessons")
        return incomplete_lessons
    
    def process_single_lesson(self, lesson: Dict[str, Any]) -> bool:
        """Process a single lesson using the comprehensive generator"""
        print(f"\n🔧 PROCESSING: {lesson['title']}")
        print(f"Sequence Order: {lesson['sequence_order']}")
        
        # Create a focused script to complete this lesson
        script_content = f'''
import sys
sys.path.append('/Users/<USER>/Documents/NIRA/NIRA/Scripts')

from comprehensive_lesson_generator import ComprehensiveLessonGenerator
import json

def complete_lesson():
    generator = ComprehensiveLessonGenerator()
    
    # Generate content for this specific lesson
    lesson_data = generator.generate_comprehensive_lesson_content(
        "{lesson['title']}", 
        {lesson['sequence_order']}
    )
    
    if lesson_data:
        # Update the database
        success = generator.update_lesson_in_database(lesson_data)
        
        if success:
            print("✅ Lesson completed successfully")
            return True
        else:
            print("❌ Database update failed")
            return False
    else:
        print("❌ Content generation failed")
        return False

if __name__ == "__main__":
    complete_lesson()
'''
        
        # Write temporary script
        temp_script = f"/tmp/complete_lesson_{lesson['sequence_order']}.py"
        with open(temp_script, 'w') as f:
            f.write(script_content)
        
        try:
            # Run the script
            result = subprocess.run(
                [sys.executable, temp_script],
                capture_output=True,
                text=True,
                timeout=300,
                cwd="/Users/<USER>/Documents/NIRA/NIRA/Scripts"
            )
            
            # Clean up
            os.remove(temp_script)
            
            if result.returncode == 0:
                print(f"✅ Successfully completed {lesson['title']}")
                return True
            else:
                print(f"❌ Failed to complete {lesson['title']}")
                print(f"Error: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print(f"⏰ Timeout processing {lesson['title']}")
            if os.path.exists(temp_script):
                os.remove(temp_script)
            return False
        except Exception as e:
            print(f"❌ Error processing {lesson['title']}: {e}")
            if os.path.exists(temp_script):
                os.remove(temp_script)
            return False
    
    def validate_lesson_completion(self, lesson_id: str) -> bool:
        """Validate that a lesson was completed properly"""
        params = {
            'select': 'content_metadata',
            'id': f'eq.{lesson_id}'
        }
        
        response = requests.get(f"{SUPABASE_URL}/rest/v1/lessons", headers=self.headers, params=params)
        
        if response.status_code != 200:
            return False
        
        lessons = response.json()
        if not lessons:
            return False
        
        metadata = lessons[0].get('content_metadata', {})
        
        # Check completion criteria
        vocab_count = len(metadata.get('vocabulary', []))
        conv_count = len(metadata.get('conversations', []))
        grammar_count = len(metadata.get('grammar_points', []))
        exercise_count = len(metadata.get('exercises', []))
        
        is_complete = (vocab_count >= 25 and conv_count >= 15 and 
                      grammar_count >= 10 and exercise_count >= 5)
        
        print(f"📊 Validation: {vocab_count}v, {conv_count}c, {grammar_count}g, {exercise_count}e")
        
        return is_complete
    
    def process_all_incomplete_lessons(self) -> Dict[str, Any]:
        """Process all incomplete lessons systematically"""
        print("🚀 TARGETED LESSON PROCESSING")
        print("Following Complete_Lesson_Implementation_Guide.md")
        print("Following COMPREHENSIVE_QUALITY_CHECKLIST.md")
        print("=" * 60)
        
        incomplete_lessons = self.get_incomplete_lessons()
        
        if not incomplete_lessons:
            print("🎉 No incomplete lessons found!")
            return {'status': 'all_complete'}
        
        results = {
            'total_lessons': len(incomplete_lessons),
            'processed': 0,
            'successful': 0,
            'failed': [],
            'completed_lessons': []
        }
        
        for i, lesson in enumerate(incomplete_lessons, 1):
            print(f"\n📖 Processing {i}/{len(incomplete_lessons)}: {lesson['title']}")
            
            # Process the lesson
            if self.process_single_lesson(lesson):
                # Validate completion
                if self.validate_lesson_completion(lesson['id']):
                    results['successful'] += 1
                    results['completed_lessons'].append(lesson['title'])
                    print(f"✅ {lesson['title']} completed and validated")
                else:
                    results['failed'].append(f"{lesson['title']}: Validation failed")
                    print(f"⚠️ {lesson['title']} processed but validation failed")
            else:
                results['failed'].append(f"{lesson['title']}: Processing failed")
                print(f"❌ {lesson['title']} processing failed")
            
            results['processed'] += 1
            
            # Rate limiting between lessons
            time.sleep(5)
        
        return results

def main():
    """Main function"""
    processor = TargetedLessonProcessor()
    
    # Process all incomplete lessons
    results = processor.process_all_incomplete_lessons()
    
    # Display results
    print(f"\n📊 FINAL RESULTS:")
    print(f"  • Total Lessons: {results.get('total_lessons', 0)}")
    print(f"  • Processed: {results.get('processed', 0)}")
    print(f"  • Successful: {results.get('successful', 0)}")
    print(f"  • Failed: {len(results.get('failed', []))}")
    
    if results.get('completed_lessons'):
        print(f"\n✅ COMPLETED LESSONS:")
        for lesson in results['completed_lessons']:
            print(f"  • {lesson}")
    
    if results.get('failed'):
        print(f"\n❌ FAILED LESSONS:")
        for failure in results['failed']:
            print(f"  • {failure}")
    
    print(f"\n📋 NEXT STEPS:")
    print("1. Verify completed lessons in iOS app")
    print("2. Generate audio for completed lessons")
    print("3. Run comprehensive quality validation")

if __name__ == "__main__":
    main()
