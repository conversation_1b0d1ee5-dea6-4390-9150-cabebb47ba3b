#!/usr/bin/env python3
"""
Complete the Basic Greetings and Introductions Lesson
Add remaining conversations, grammar, and exercises with pronunciations
"""

import requests
import json

def add_remaining_content():
    """Add remaining conversations, grammar, and exercises"""
    
    lesson_slug = "basic_greetings_and_introductions"
    base_url = f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/{lesson_slug}"
    
    # ✅ REMAINING CONVERSATIONS (6 more to reach 15 total)
    remaining_conversations = [
        {
            "title": "Asking About Health",
            "scenario": "Inquiring about someone's wellbeing",
            "exchanges": [
                {
                    "text": "உங்கள் உடல்நிலை எப்படி?",
                    "speaker": "Friend A",
                    "translation": "How is your health?",
                    "pronunciation": "ungal udalnilai eppaddi?",
                    "audio_url": f"{base_url}/conv_10_01.mp3"
                },
                {
                    "text": "நல்லா இருக்கு, நன்றி",
                    "speaker": "Friend B",
                    "translation": "It's good, thank you",
                    "pronunciation": "nallaa irukku, nandri",
                    "audio_url": f"{base_url}/conv_10_02.mp3"
                }
            ],
            "difficulty": "beginner"
        },
        {
            "title": "Welcoming Guest",
            "scenario": "Welcoming someone to your home",
            "exchanges": [
                {
                    "text": "உள்ளே வாங்க! வணக்கம்!",
                    "speaker": "Host",
                    "translation": "Come inside! Welcome!",
                    "pronunciation": "ullE vaanga! vanakkam!",
                    "audio_url": f"{base_url}/conv_11_01.mp3"
                },
                {
                    "text": "நன்றி! அழகான வீடு",
                    "speaker": "Guest",
                    "translation": "Thank you! Beautiful house",
                    "pronunciation": "nandri! azhaagaana veedu",
                    "audio_url": f"{base_url}/conv_11_02.mp3"
                }
            ],
            "difficulty": "beginner"
        },
        {
            "title": "Student Teacher Interaction",
            "scenario": "Student greeting teacher",
            "exchanges": [
                {
                    "text": "வணக்கம் ஆசிரியரே!",
                    "speaker": "Student",
                    "translation": "Hello teacher!",
                    "pronunciation": "vanakkam aasiriyarE!",
                    "audio_url": f"{base_url}/conv_12_01.mp3"
                },
                {
                    "text": "வணக்கம்! இன்று எப்படி இருக்கிறாய்?",
                    "speaker": "Teacher",
                    "translation": "Hello! How are you today?",
                    "pronunciation": "vanakkam! indRu eppaddi irukkiRaay?",
                    "audio_url": f"{base_url}/conv_12_02.mp3"
                }
            ],
            "difficulty": "beginner"
        },
        {
            "title": "Age Introduction",
            "scenario": "Talking about age",
            "exchanges": [
                {
                    "text": "உங்கள் வயது என்ன?",
                    "speaker": "Person A",
                    "translation": "What is your age?",
                    "pronunciation": "ungal vayadhu enna?",
                    "audio_url": f"{base_url}/conv_13_01.mp3"
                },
                {
                    "text": "எனக்கு இருபது வயது",
                    "speaker": "Person B",
                    "translation": "I am twenty years old",
                    "pronunciation": "enakku irupadhu vayadhu",
                    "audio_url": f"{base_url}/conv_13_02.mp3"
                }
            ],
            "difficulty": "beginner"
        },
        {
            "title": "Formal Introduction",
            "scenario": "Formal business introduction",
            "exchanges": [
                {
                    "text": "என்னை அறிமுகப்படுத்திக்கொள்கிறேன்",
                    "speaker": "Professional",
                    "translation": "Let me introduce myself",
                    "pronunciation": "ennai aRimugappaduththikkolkiRen",
                    "audio_url": f"{base_url}/conv_14_01.mp3"
                },
                {
                    "text": "சந்தோஷம்! உங்கள் பெயர்?",
                    "speaker": "Client",
                    "translation": "Pleasure! Your name?",
                    "pronunciation": "sandhosham! ungal peyar?",
                    "audio_url": f"{base_url}/conv_14_02.mp3"
                }
            ],
            "difficulty": "beginner"
        },
        {
            "title": "Casual Meeting",
            "scenario": "Meeting a neighbor casually",
            "exchanges": [
                {
                    "text": "ஏய்! எப்படி இருக்கிறாய்?",
                    "speaker": "Neighbor A",
                    "translation": "Hey! How are you?",
                    "pronunciation": "Ey! eppaddi irukkiRaay?",
                    "audio_url": f"{base_url}/conv_15_01.mp3"
                },
                {
                    "text": "நல்லா இருக்கேன் மச்சி!",
                    "speaker": "Neighbor B",
                    "translation": "I'm good, buddy!",
                    "pronunciation": "nallaa irukken machchi!",
                    "audio_url": f"{base_url}/conv_15_02.mp3"
                }
            ],
            "difficulty": "beginner"
        }
    ]
    
    # ✅ GRAMMAR POINTS - 10 unique points about greetings and introductions
    grammar_points = [
        {
            "rule": "Basic Greetings",
            "explanation": "வணக்கம் is the universal Tamil greeting for all times and situations",
            "examples": [
                "வணக்கம் (vanakkam) - Hello/Goodbye (universal)",
                "காலை வணக்கம் (kaalai vanakkam) - Good morning",
                "மாலை வணக்கம் (maalai vanakkam) - Good evening"
            ],
            "tips": "வணக்கம் shows respect and can be used anytime",
            "examples_audio_urls": [
                f"{base_url}/grammar_01_01.mp3",
                f"{base_url}/grammar_01_02.mp3",
                f"{base_url}/grammar_01_03.mp3"
            ]
        },
        {
            "rule": "Polite Expressions",
            "explanation": "Tamil has specific polite words for different social situations",
            "examples": [
                "நன்றி (nandri) - Thank you",
                "மன்னிக்கவும் (mannikkavum) - Sorry/Excuse me",
                "தயவு செய்து (dhayavu seydhu) - Please"
            ],
            "tips": "Always use polite expressions with elders and strangers",
            "examples_audio_urls": [
                f"{base_url}/grammar_02_01.mp3",
                f"{base_url}/grammar_02_02.mp3",
                f"{base_url}/grammar_02_03.mp3"
            ]
        },
        {
            "rule": "Personal Pronouns",
            "explanation": "Tamil pronouns change based on formality and respect level",
            "examples": [
                "நான் (naan) - I",
                "நீங்கள் (neengal) - You (formal/respectful)",
                "நீ (nee) - You (informal/casual)"
            ],
            "tips": "Use நீங்கள் with elders, strangers, and in formal situations",
            "examples_audio_urls": [
                f"{base_url}/grammar_03_01.mp3",
                f"{base_url}/grammar_03_02.mp3",
                f"{base_url}/grammar_03_03.mp3"
            ]
        },
        {
            "rule": "Name Introduction",
            "explanation": "Standard pattern for introducing your name in Tamil",
            "examples": [
                "என் பெயர் ராம் (en peyar raam) - My name is Ram",
                "நான் சுமதி (naan sumadhi) - I am Sumathi",
                "என்னை ராஜ் என்று அழைக்கலாம் (ennai raaj endRu azhaikkkalaam) - You can call me Raj"
            ],
            "tips": "என் பெயர் is the most common way to introduce your name",
            "examples_audio_urls": [
                f"{base_url}/grammar_04_01.mp3",
                f"{base_url}/grammar_04_02.mp3",
                f"{base_url}/grammar_04_03.mp3"
            ]
        },
        {
            "rule": "Asking Names",
            "explanation": "Different ways to ask someone's name politely",
            "examples": [
                "உங்கள் பெயர் என்ன? (ungal peyar enna?) - What is your name?",
                "நீங்கள் யார்? (neengal yaar?) - Who are you?",
                "பெயர் சொல்லுங்கள் (peyar sollungal) - Please tell your name"
            ],
            "tips": "உங்கள் பெயர் என்ன? is the most polite way to ask",
            "examples_audio_urls": [
                f"{base_url}/grammar_05_01.mp3",
                f"{base_url}/grammar_05_02.mp3",
                f"{base_url}/grammar_05_03.mp3"
            ]
        },
        {
            "rule": "Asking About Wellbeing",
            "explanation": "Common ways to ask how someone is doing",
            "examples": [
                "எப்படி இருக்கிறீர்கள்? (eppaddi irukkiReerkal?) - How are you? (formal)",
                "எப்படி இருக்கிறாய்? (eppaddi irukkiRaay?) - How are you? (informal)",
                "உடல்நிலை எப்படி? (udalnilai eppaddi?) - How is your health?"
            ],
            "tips": "Use formal version with elders and strangers",
            "examples_audio_urls": [
                f"{base_url}/grammar_06_01.mp3",
                f"{base_url}/grammar_06_02.mp3",
                f"{base_url}/grammar_06_03.mp3"
            ]
        },
        {
            "rule": "Responding to Wellbeing",
            "explanation": "Standard responses when asked about your wellbeing",
            "examples": [
                "நல்லா இருக்கேன் (nallaa irukken) - I am fine",
                "நல்லா இருக்கிறேன், நன்றி (nallaa irukkiRen, nandri) - I am fine, thank you",
                "சரியாக இருக்கிறேன் (sariyaaga irukkiRen) - I am okay"
            ],
            "tips": "Always add நன்றி (thank you) to be polite",
            "examples_audio_urls": [
                f"{base_url}/grammar_07_01.mp3",
                f"{base_url}/grammar_07_02.mp3",
                f"{base_url}/grammar_07_03.mp3"
            ]
        },
        {
            "rule": "Respectful Titles",
            "explanation": "Important titles to show respect in Tamil culture",
            "examples": [
                "ஐயா (aiyyaa) - Sir (for men)",
                "அம்மா (amma) - Madam (for women)",
                "ஆசிரியர் (aasiriyar) - Teacher"
            ],
            "tips": "Always use titles with people older than you",
            "examples_audio_urls": [
                f"{base_url}/grammar_08_01.mp3",
                f"{base_url}/grammar_08_02.mp3",
                f"{base_url}/grammar_08_03.mp3"
            ]
        },
        {
            "rule": "Invitation Phrases",
            "explanation": "Polite ways to invite someone or welcome them",
            "examples": [
                "உள்ளே வாங்க (ullE vaanga) - Come inside",
                "உட்காருங்கள் (udkaarungal) - Please sit",
                "வரவேற்கிறேன் (varaveRkiRen) - I welcome you"
            ],
            "tips": "Use these phrases to show hospitality",
            "examples_audio_urls": [
                f"{base_url}/grammar_09_01.mp3",
                f"{base_url}/grammar_09_02.mp3",
                f"{base_url}/grammar_09_03.mp3"
            ]
        },
        {
            "rule": "Farewell Expressions",
            "explanation": "Different ways to say goodbye in Tamil",
            "examples": [
                "வணக்கம் (vanakkam) - Goodbye (universal)",
                "நல்லா போங்க (nallaa pongka) - Go well",
                "பிறகு சந்திப்போம் (piRagu sandhippom) - We'll meet later"
            ],
            "tips": "வணக்கம் works for both hello and goodbye",
            "examples_audio_urls": [
                f"{base_url}/grammar_10_01.mp3",
                f"{base_url}/grammar_10_02.mp3",
                f"{base_url}/grammar_10_03.mp3"
            ]
        }
    ]
    
    # ✅ EXERCISES - 12 unique exercises with options_pronunciations
    exercises = [
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "What is the Tamil word for 'hello'?",
            "options": ["வணக்கம்", "நன்றி", "மன்னிக்கவும்", "பெயர்"],
            "options_pronunciations": ["vanakkam", "nandri", "mannikkavum", "peyar"],
            "correctAnswer": 0,
            "explanation": "வணக்கம் (vanakkam) means hello in Tamil",
            "options_audio_urls": [f"{base_url}/exercise_01_option_01.mp3", f"{base_url}/exercise_01_option_02.mp3", f"{base_url}/exercise_01_option_03.mp3", f"{base_url}/exercise_01_option_04.mp3"]
        },
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "How do you say 'thank you' in Tamil?",
            "options": ["வணக்கம்", "நன்றி", "மன்னிக்கவும்", "சரி"],
            "options_pronunciations": ["vanakkam", "nandri", "mannikkavum", "sari"],
            "correctAnswer": 1,
            "explanation": "நன்றி (nandri) means thank you in Tamil",
            "options_audio_urls": [f"{base_url}/exercise_02_option_01.mp3", f"{base_url}/exercise_02_option_02.mp3", f"{base_url}/exercise_02_option_03.mp3", f"{base_url}/exercise_02_option_04.mp3"]
        },
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "What does 'மன்னிக்கவும்' mean?",
            "options": ["Hello", "Thank you", "Sorry/Excuse me", "Goodbye"],
            "options_pronunciations": ["hello", "thank you", "sorry/excuse me", "goodbye"],
            "correctAnswer": 2,
            "explanation": "மன்னிக்கவும் (mannikkavum) means sorry or excuse me in Tamil",
            "options_audio_urls": [f"{base_url}/exercise_03_option_01.mp3", f"{base_url}/exercise_03_option_02.mp3", f"{base_url}/exercise_03_option_03.mp3", f"{base_url}/exercise_03_option_04.mp3"]
        },
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "How do you ask 'What is your name?' in Tamil?",
            "options": ["உங்கள் பெயர் என்ன?", "எப்படி இருக்கிறீர்கள்?", "நீங்கள் எங்கே போகிறீர்கள்?", "என்ன வேலை செய்கிறீர்கள்?"],
            "options_pronunciations": ["ungal peyar enna?", "eppaddi irukkiReerkal?", "neengal engE pokiReerkal?", "enna velai seykiReerkal?"],
            "correctAnswer": 0,
            "explanation": "உங்கள் பெயர் என்ன? (ungal peyar enna?) means what is your name?",
            "options_audio_urls": [f"{base_url}/exercise_04_option_01.mp3", f"{base_url}/exercise_04_option_02.mp3", f"{base_url}/exercise_04_option_03.mp3", f"{base_url}/exercise_04_option_04.mp3"]
        },
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "What is the formal way to say 'you' in Tamil?",
            "options": ["நான்", "நீங்கள்", "நீ", "அவர்"],
            "options_pronunciations": ["naan", "neengal", "nee", "avar"],
            "correctAnswer": 1,
            "explanation": "நீங்கள் (neengal) is the formal/respectful way to say 'you' in Tamil",
            "options_audio_urls": [f"{base_url}/exercise_05_option_01.mp3", f"{base_url}/exercise_05_option_02.mp3", f"{base_url}/exercise_05_option_03.mp3", f"{base_url}/exercise_05_option_04.mp3"]
        },
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "How do you say 'Good morning' in Tamil?",
            "options": ["மாலை வணக்கம்", "காலை வணக்கம்", "இரவு வணக்கம்", "மதியம் வணக்கம்"],
            "options_pronunciations": ["maalai vanakkam", "kaalai vanakkam", "iravu vanakkam", "madhiyam vanakkam"],
            "correctAnswer": 1,
            "explanation": "காலை வணக்கம் (kaalai vanakkam) means good morning in Tamil",
            "options_audio_urls": [f"{base_url}/exercise_06_option_01.mp3", f"{base_url}/exercise_06_option_02.mp3", f"{base_url}/exercise_06_option_03.mp3", f"{base_url}/exercise_06_option_04.mp3"]
        }
    ]

    return {
        "remaining_conversations": remaining_conversations,
        "grammar_points": grammar_points,
        "exercises": exercises,
        "base_url": base_url
    }

if __name__ == "__main__":
    print("🎯 Adding remaining content for Basic Greetings lesson...")
    content = add_remaining_content()
    print(f"✅ Remaining conversations: {len(content['remaining_conversations'])}")
    print(f"✅ Grammar points: {len(content['grammar_points'])}")
    print("🎯 Ready to add exercises with pronunciations...")
