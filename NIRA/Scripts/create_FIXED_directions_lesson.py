#!/usr/bin/env python3
"""
FIXED Directions and Locations Lesson
NO REPEATS - Every conversation, grammar, exercise is UNIQUE
ALL have romanized Tamil where required
"""

import requests
import json

def create_fixed_directions_lesson():
    """Create FIXED Directions lesson with NO REPEATS"""
    
    lesson_id = "3318f37a-adef-4dd2-9f7a-6f8a2618cd38"
    lesson_slug = "directions_and_locations"
    base_url = f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/{lesson_slug}"
    
    # ✅ VOCABULARY - Same as before (was correct)
    vocabulary = [
        {
            "word": "திசை",
            "translation": "Direction",
            "pronunciation": "thisai",
            "example": "எந்த திசையில் போக வேண்டும்? (endha thisaiyil poga vendum?) - Which direction should we go?",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_01_word.mp3",
            "example_audio_url": f"{base_url}/vocab_01_example.mp3"
        },
        {
            "word": "வடக்கு",
            "translation": "North",
            "pronunciation": "vadakku",
            "example": "வடக்கு திசையில் மலைகள் உள்ளன (vadakku thisaiyil malaigal ullan) - There are mountains in the north direction",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_02_word.mp3",
            "example_audio_url": f"{base_url}/vocab_02_example.mp3"
        },
        {
            "word": "தெற்கு",
            "translation": "South",
            "pronunciation": "therku",
            "example": "தெற்கு திசையில் கடல் இருக்கிறது (therku thisaiyil kadal irukkiRadhu) - The sea is in the south direction",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_03_word.mp3",
            "example_audio_url": f"{base_url}/vocab_03_example.mp3"
        },
        {
            "word": "கிழக்கு",
            "translation": "East",
            "pronunciation": "kizhakku",
            "example": "கிழக்கு திசையில் சூரியன் உதிக்கிறது (kizhakku thisaiyil sooriyan udhikkiRadhu) - The sun rises in the east direction",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_04_word.mp3",
            "example_audio_url": f"{base_url}/vocab_04_example.mp3"
        },
        {
            "word": "மேற்கு",
            "translation": "West",
            "pronunciation": "merku",
            "example": "மேற்கு திசையில் சூரியன் மறைகிறது (merku thisaiyil sooriyan maRaikkiradhu) - The sun sets in the west direction",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_05_word.mp3",
            "example_audio_url": f"{base_url}/vocab_05_example.mp3"
        },
        {
            "word": "இடது",
            "translation": "Left",
            "pronunciation": "idathu",
            "example": "இடது பக்கம் திரும்புங்கள் (idathu pakkam thirumbungal) - Turn to the left side",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_06_word.mp3",
            "example_audio_url": f"{base_url}/vocab_06_example.mp3"
        },
        {
            "word": "வலது",
            "translation": "Right",
            "pronunciation": "valathu",
            "example": "வலது பக்கம் திரும்புங்கள் (valathu pakkam thirumbungal) - Turn to the right side",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_07_word.mp3",
            "example_audio_url": f"{base_url}/vocab_07_example.mp3"
        },
        {
            "word": "நேராக",
            "translation": "Straight",
            "pronunciation": "neraaga",
            "example": "நேராக போங்கள் (neraaga pongal) - Go straight",
            "difficulty": "basic",
            "part_of_speech": "adverb",
            "word_audio_url": f"{base_url}/vocab_08_word.mp3",
            "example_audio_url": f"{base_url}/vocab_08_example.mp3"
        },
        {
            "word": "சாலை",
            "translation": "Road",
            "pronunciation": "saalai",
            "example": "பெரிய சாலையில் போகிறோம் (periya saalaiyil pokiRom) - We are going on the big road",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_09_word.mp3",
            "example_audio_url": f"{base_url}/vocab_09_example.mp3"
        },
        {
            "word": "தெரு",
            "translation": "Street",
            "pronunciation": "theru",
            "example": "இந்த தெருவில் கடைகள் உள்ளன (indha theruvil kadaigal ullan) - There are shops on this street",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_10_word.mp3",
            "example_audio_url": f"{base_url}/vocab_10_example.mp3"
        },
        {
            "word": "முக்கோணம்",
            "translation": "Junction",
            "pronunciation": "mukkonam",
            "example": "முக்கோணத்தில் இடது பக்கம் திரும்புங்கள் (mukkonaththil idathu pakkam thirumbungal) - Turn left at the junction",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_11_word.mp3",
            "example_audio_url": f"{base_url}/vocab_11_example.mp3"
        },
        {
            "word": "பாலம்",
            "translation": "Bridge",
            "pronunciation": "paalam",
            "example": "பாலத்தை கடந்து போங்கள் (palaaththai kadandhu pongal) - Cross the bridge and go",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_12_word.mp3",
            "example_audio_url": f"{base_url}/vocab_12_example.mp3"
        },
        {
            "word": "கட்டிடம்",
            "translation": "Building",
            "pronunciation": "kattidam",
            "example": "உயரமான கட்டிடம் தெரிகிறது (uyaramaana kattidam therikkiradhu) - A tall building is visible",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_13_word.mp3",
            "example_audio_url": f"{base_url}/vocab_13_example.mp3"
        },
        {
            "word": "மூலை",
            "translation": "Corner",
            "pronunciation": "moolai",
            "example": "மூலையில் கடை இருக்கிறது (moolaiyil kadai irukkiRadhu) - There is a shop at the corner",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_14_word.mp3",
            "example_audio_url": f"{base_url}/vocab_14_example.mp3"
        },
        {
            "word": "அருகில்",
            "translation": "Near",
            "pronunciation": "arugil",
            "example": "பள்ளி அருகில் இருக்கிறது (palli arugil irukkiRadhu) - The school is nearby",
            "difficulty": "basic",
            "part_of_speech": "adverb",
            "word_audio_url": f"{base_url}/vocab_15_word.mp3",
            "example_audio_url": f"{base_url}/vocab_15_example.mp3"
        },
        {
            "word": "தூரம்",
            "translation": "Distance",
            "pronunciation": "thoorem",
            "example": "எவ்வளவு தூரம் போக வேண்டும்? (evvaLavu thoorem poga vendum?) - How much distance do we need to go?",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_16_word.mp3",
            "example_audio_url": f"{base_url}/vocab_16_example.mp3"
        },
        {
            "word": "இடம்",
            "translation": "Place",
            "pronunciation": "idam",
            "example": "இந்த இடம் அழகாக இருக்கிறது (indha idam azhaagaaga irukkiRadhu) - This place is beautiful",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_17_word.mp3",
            "example_audio_url": f"{base_url}/vocab_17_example.mp3"
        },
        {
            "word": "வழி",
            "translation": "Way/Path",
            "pronunciation": "vazhi",
            "example": "சரியான வழி எது? (sariyaana vazhi edhu?) - Which is the correct way?",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_18_word.mp3",
            "example_audio_url": f"{base_url}/vocab_18_example.mp3"
        },
        {
            "word": "முன்",
            "translation": "Front",
            "pronunciation": "mun",
            "example": "கடையின் முன் நிற்கிறேன் (kadaiyin mun nirkiRen) - I am standing in front of the shop",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_19_word.mp3",
            "example_audio_url": f"{base_url}/vocab_19_example.mp3"
        },
        {
            "word": "பின்",
            "translation": "Behind",
            "pronunciation": "pin",
            "example": "வீட்டின் பின் தோட்டம் இருக்கிறது (veettin pin thottam irukkiRadhu) - There is a garden behind the house",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_20_word.mp3",
            "example_audio_url": f"{base_url}/vocab_20_example.mp3"
        },
        {
            "word": "மேல்",
            "translation": "Above/Up",
            "pronunciation": "mel",
            "example": "மேல் மாடியில் அலுவலகம் இருக்கிறது (mel maadiyil aluvalagatham irukkiRadhu) - The office is on the upper floor",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_21_word.mp3",
            "example_audio_url": f"{base_url}/vocab_21_example.mp3"
        },
        {
            "word": "கீழ்",
            "translation": "Below/Down",
            "pronunciation": "keezh",
            "example": "கீழ் மாடியில் கடை இருக்கிறது (keezh maadiyil kadai irukkiRadhu) - There is a shop on the ground floor",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_22_word.mp3",
            "example_audio_url": f"{base_url}/vocab_22_example.mp3"
        },
        {
            "word": "நடு",
            "translation": "Middle/Center",
            "pronunciation": "nadu",
            "example": "சாலையின் நடுவில் போகாதீர்கள் (saalaiyyin naduvil pogaadheerkal) - Don't go in the middle of the road",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_23_word.mp3",
            "example_audio_url": f"{base_url}/vocab_23_example.mp3"
        },
        {
            "word": "எதிர்",
            "translation": "Opposite",
            "pronunciation": "edhir",
            "example": "பள்ளிக்கு எதிரில் கோயில் இருக்கிறது (pallikku edhiril koyil irukkiRadhu) - There is a temple opposite to the school",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_24_word.mp3",
            "example_audio_url": f"{base_url}/vocab_24_example.mp3"
        },
        {
            "word": "முகவரி",
            "translation": "Address",
            "pronunciation": "mugavari",
            "example": "உங்கள் முகவரி என்ன? (ungal mugavari enna?) - What is your address?",
            "difficulty": "basic",
            "part_of_speech": "noun",
            "word_audio_url": f"{base_url}/vocab_25_word.mp3",
            "example_audio_url": f"{base_url}/vocab_25_example.mp3"
        }
    ]
    
    # ✅ CONVERSATIONS - ALL 15 UNIQUE with romanized Tamil
    conversations = [
        {
            "title": "Asking for Directions",
            "scenario": "Tourist asking for directions to a temple",
            "difficulty": "beginner",
            "exchanges": [
                {
                    "text": "மன்னிக்கவும், கோயில் எங்கே இருக்கிறது?",
                    "speaker": "Tourist",
                    "translation": "Excuse me, where is the temple?",
                    "pronunciation": "mannikkavum, koyil engE irukkiRadhu?",
                    "audio_url": f"{base_url}/conv_01_01.mp3"
                },
                {
                    "text": "நேராக போய் இடது பக்கம் திரும்புங்கள்",
                    "speaker": "Local",
                    "translation": "Go straight and turn left",
                    "pronunciation": "neraaga poyi idathu pakkam thirumbungal",
                    "audio_url": f"{base_url}/conv_01_02.mp3"
                }
            ]
        },
        {
            "title": "Finding a Shop",
            "scenario": "Looking for a specific shop location",
            "difficulty": "beginner",
            "exchanges": [
                {
                    "text": "மருந்து கடை எந்த தெருவில் இருக்கிறது?",
                    "speaker": "Customer",
                    "translation": "Which street is the pharmacy on?",
                    "pronunciation": "marundhu kadai endha theruvil irukkiRadhu?",
                    "audio_url": f"{base_url}/conv_02_01.mp3"
                },
                {
                    "text": "பாலத்தை கடந்து வலது பக்கம் இருக்கிறது",
                    "speaker": "Helper",
                    "translation": "Cross the bridge and it's on the right side",
                    "pronunciation": "palaaththai kadandhu valathu pakkam irukkiRadhu",
                    "audio_url": f"{base_url}/conv_02_02.mp3"
                }
            ]
        },
        {
            "title": "Lost in the City",
            "scenario": "Someone is lost and needs help",
            "difficulty": "beginner",
            "exchanges": [
                {
                    "text": "நான் வழி தெரியாமல் இருக்கிறேன்",
                    "speaker": "Lost Person",
                    "translation": "I don't know the way",
                    "pronunciation": "naan vazhi theriyaamal irukkiRen",
                    "audio_url": f"{base_url}/conv_03_01.mp3"
                },
                {
                    "text": "எங்கே போக வேண்டும்?",
                    "speaker": "Helper",
                    "translation": "Where do you need to go?",
                    "pronunciation": "engE poga vendum?",
                    "audio_url": f"{base_url}/conv_03_02.mp3"
                }
            ]
        },
        {
            "title": "Bus Stop Location",
            "scenario": "Finding the nearest bus stop",
            "difficulty": "beginner",
            "exchanges": [
                {
                    "text": "பேருந்து நிறுத்தம் எங்கே உள்ளது?",
                    "speaker": "Traveler",
                    "translation": "Where is the bus stop?",
                    "pronunciation": "perundhu niruththam engE ulladhu?",
                    "audio_url": f"{base_url}/conv_04_01.mp3"
                },
                {
                    "text": "அந்த மூலையில் இருக்கிறது",
                    "speaker": "Local",
                    "translation": "It's at that corner",
                    "pronunciation": "andha moolaiyil irukkiRadhu",
                    "audio_url": f"{base_url}/conv_04_02.mp3"
                }
            ]
        },
        {
            "title": "Hospital Directions",
            "scenario": "Emergency directions to hospital",
            "difficulty": "beginner",
            "exchanges": [
                {
                    "text": "மருத்துவமனை எவ்வளவு தூரம்?",
                    "speaker": "Person",
                    "translation": "How far is the hospital?",
                    "pronunciation": "maruththuvamanai evvaLavu thoorem?",
                    "audio_url": f"{base_url}/conv_05_01.mp3"
                },
                {
                    "text": "இரண்டு கிலோமீட்டர் தூரம்",
                    "speaker": "Helper",
                    "translation": "Two kilometers distance",
                    "pronunciation": "irandu kilomeettar thoorem",
                    "audio_url": f"{base_url}/conv_05_02.mp3"
                }
            ]
        },
        {
            "title": "School Location",
            "scenario": "Parent looking for school",
            "difficulty": "beginner",
            "exchanges": [
                {
                    "text": "பள்ளி எந்த திசையில் இருக்கிறது?",
                    "speaker": "Parent",
                    "translation": "Which direction is the school in?",
                    "pronunciation": "palli endha thisaiyil irukkiRadhu?",
                    "audio_url": f"{base_url}/conv_06_01.mp3"
                },
                {
                    "text": "வடக்கு திசையில் இருக்கிறது",
                    "speaker": "Local",
                    "translation": "It's in the north direction",
                    "pronunciation": "vadakku thisaiyil irukkiRadhu",
                    "audio_url": f"{base_url}/conv_06_02.mp3"
                }
            ]
        },
        {
            "title": "Market Directions",
            "scenario": "Finding the local market",
            "difficulty": "beginner",
            "exchanges": [
                {
                    "text": "சந்தை எந்த சாலையில் இருக்கிறது?",
                    "speaker": "Shopper",
                    "translation": "Which road is the market on?",
                    "pronunciation": "sandhai endha saalaiyil irukkiRadhu?",
                    "audio_url": f"{base_url}/conv_07_01.mp3"
                },
                {
                    "text": "பெரிய சாலையில் இருக்கிறது",
                    "speaker": "Local",
                    "translation": "It's on the main road",
                    "pronunciation": "periya saalaiyil irukkiRadhu",
                    "audio_url": f"{base_url}/conv_07_02.mp3"
                }
            ]
        },
        {
            "title": "ATM Location",
            "scenario": "Looking for nearest ATM",
            "difficulty": "beginner",
            "exchanges": [
                {
                    "text": "ஏ.டி.எம் அருகில் எங்கே இருக்கிறது?",
                    "speaker": "Customer",
                    "translation": "Where is the nearest ATM?",
                    "pronunciation": "ATM arugil engE irukkiRadhu?",
                    "audio_url": f"{base_url}/conv_08_01.mp3"
                },
                {
                    "text": "வங்கிக்கு எதிரில் இருக்கிறது",
                    "speaker": "Helper",
                    "translation": "It's opposite to the bank",
                    "pronunciation": "vangikku edhiril irukkiRadhu",
                    "audio_url": f"{base_url}/conv_08_02.mp3"
                }
            ]
        },
        {
            "title": "Restaurant Location",
            "scenario": "Finding a good restaurant",
            "difficulty": "beginner",
            "exchanges": [
                {
                    "text": "நல்ல உணவகம் எங்கே இருக்கிறது?",
                    "speaker": "Tourist",
                    "translation": "Where is a good restaurant?",
                    "pronunciation": "nalla unavagam engE irukkiRadhu?",
                    "audio_url": f"{base_url}/conv_09_01.mp3"
                },
                {
                    "text": "கோயிலின் பின் இருக்கிறது",
                    "speaker": "Local",
                    "translation": "It's behind the temple",
                    "pronunciation": "koyilin pin irukkiRadhu",
                    "audio_url": f"{base_url}/conv_09_02.mp3"
                }
            ]
        },
        {
            "title": "Police Station",
            "scenario": "Emergency - finding police station",
            "difficulty": "beginner",
            "exchanges": [
                {
                    "text": "காவல் நிலையம் எங்கே இருக்கிறது?",
                    "speaker": "Person",
                    "translation": "Where is the police station?",
                    "pronunciation": "kaaval nilayam engE irukkiRadhu?",
                    "audio_url": f"{base_url}/conv_10_01.mp3"
                },
                {
                    "text": "முக்கோணத்தில் நேராக போங்கள்",
                    "speaker": "Helper",
                    "translation": "Go straight at the junction",
                    "pronunciation": "mukkonaththil neraaga pongal",
                    "audio_url": f"{base_url}/conv_10_02.mp3"
                }
            ]
        },
        {
            "title": "Post Office",
            "scenario": "Finding post office for mail",
            "difficulty": "beginner",
            "exchanges": [
                {
                    "text": "தபால் அலுவலகம் எந்த கட்டிடத்தில்?",
                    "speaker": "Customer",
                    "translation": "Which building is the post office in?",
                    "pronunciation": "thapal aluvalagatham endha kattidaththil?",
                    "audio_url": f"{base_url}/conv_11_01.mp3"
                },
                {
                    "text": "நீல நிற கட்டிடத்தில் இருக்கிறது",
                    "speaker": "Helper",
                    "translation": "It's in the blue colored building",
                    "pronunciation": "neela niRa kattidaththil irukkiRadhu",
                    "audio_url": f"{base_url}/conv_11_02.mp3"
                }
            ]
        },
        {
            "title": "Petrol Pump",
            "scenario": "Driver looking for fuel station",
            "difficulty": "beginner",
            "exchanges": [
                {
                    "text": "பெட்ரோல் பம்ப் எவ்வளவு தூரத்தில்?",
                    "speaker": "Driver",
                    "translation": "How far is the petrol pump?",
                    "pronunciation": "petrol pump evvaLavu thooraththil?",
                    "audio_url": f"{base_url}/conv_12_01.mp3"
                },
                {
                    "text": "ஐநூறு மீட்டர் தூரத்தில் இருக்கிறது",
                    "speaker": "Helper",
                    "translation": "It's five hundred meters away",
                    "pronunciation": "ainooru meettar thooraththil irukkiRadhu",
                    "audio_url": f"{base_url}/conv_12_02.mp3"
                }
            ]
        },
        {
            "title": "Library Location",
            "scenario": "Student looking for library",
            "difficulty": "beginner",
            "exchanges": [
                {
                    "text": "நூலகம் எந்த மாடியில் இருக்கிறது?",
                    "speaker": "Student",
                    "translation": "Which floor is the library on?",
                    "pronunciation": "noolagatham endha maadiyil irukkiRadhu?",
                    "audio_url": f"{base_url}/conv_13_01.mp3"
                },
                {
                    "text": "மூன்றாம் மாடியில் இருக்கிறது",
                    "speaker": "Helper",
                    "translation": "It's on the third floor",
                    "pronunciation": "moonRaam maadiyil irukkiRadhu",
                    "audio_url": f"{base_url}/conv_13_02.mp3"
                }
            ]
        },
        {
            "title": "Park Entrance",
            "scenario": "Family looking for park entrance",
            "difficulty": "beginner",
            "exchanges": [
                {
                    "text": "பூங்காவின் நுழைவாயில் எங்கே?",
                    "speaker": "Parent",
                    "translation": "Where is the park entrance?",
                    "pronunciation": "poongaavin nuzhaivaayal engE?",
                    "audio_url": f"{base_url}/conv_14_01.mp3"
                },
                {
                    "text": "கிழக்கு பக்கத்தில் இருக்கிறது",
                    "speaker": "Guard",
                    "translation": "It's on the east side",
                    "pronunciation": "kizhakku pakkaththil irukkiRadhu",
                    "audio_url": f"{base_url}/conv_14_02.mp3"
                }
            ]
        },
        {
            "title": "Train Station",
            "scenario": "Traveler looking for railway station",
            "difficulty": "beginner",
            "exchanges": [
                {
                    "text": "ரயில் நிலையம் எந்த வழியில் போக வேண்டும்?",
                    "speaker": "Traveler",
                    "translation": "Which way should I go to the railway station?",
                    "pronunciation": "rayil nilayam endha vazhiyil poga vendum?",
                    "audio_url": f"{base_url}/conv_15_01.mp3"
                },
                {
                    "text": "மேற்கு திசையில் போங்கள்",
                    "speaker": "Local",
                    "translation": "Go in the west direction",
                    "pronunciation": "merku thisaiyil pongal",
                    "audio_url": f"{base_url}/conv_15_02.mp3"
                }
            ]
        }
    ]

    # ✅ GRAMMAR POINTS - ALL 10 UNIQUE
    grammar_points = [
        {
            "rule": "Cardinal Directions",
            "explanation": "Tamil uses specific words for the four main directions",
            "examples": [
                "வடக்கு (vadakku) - North",
                "தெற்கு (therku) - South",
                "கிழக்கு (kizhakku) - East",
                "மேற்கு (merku) - West"
            ],
            "tips": "Remember: கிழக்கு (east) where sun rises, மேற்கு (west) where sun sets",
            "examples_audio_urls": [f"{base_url}/grammar_01_01.mp3", f"{base_url}/grammar_01_02.mp3", f"{base_url}/grammar_01_03.mp3"]
        },
        {
            "rule": "Left and Right",
            "explanation": "Use இடது and வலது with பக்கம் for turning directions",
            "examples": [
                "இடது பக்கம் (idathu pakkam) - left side",
                "வலது பக்கம் (valathu pakkam) - right side",
                "திரும்புங்கள் (thirumbungal) - please turn"
            ],
            "tips": "Always use பக்கம் (side) with இடது/வலது for directions",
            "examples_audio_urls": [f"{base_url}/grammar_02_01.mp3", f"{base_url}/grammar_02_02.mp3", f"{base_url}/grammar_02_03.mp3"]
        },
        {
            "rule": "Location Questions",
            "explanation": "Use எங்கே to ask where something is located",
            "examples": [
                "எங்கே இருக்கிறது? (engE irukkiRadhu?) - Where is it?",
                "எந்த இடத்தில்? (endha idaththil?) - In which place?",
                "எவ்வளவு தூரம்? (evvaLavu thoorem?) - How far?"
            ],
            "tips": "எங்கே is the most common way to ask for location",
            "examples_audio_urls": [f"{base_url}/grammar_03_01.mp3", f"{base_url}/grammar_03_02.mp3", f"{base_url}/grammar_03_03.mp3"]
        },
        {
            "rule": "Relative Position Words",
            "explanation": "Words to describe position relative to landmarks",
            "examples": [
                "அருகில் (arugil) - near/close to",
                "எதிரில் (edhiril) - opposite to",
                "பின் (pin) - behind"
            ],
            "tips": "Add இல் to position words: அருகில், எதிரில், etc.",
            "examples_audio_urls": [f"{base_url}/grammar_04_01.mp3", f"{base_url}/grammar_04_02.mp3", f"{base_url}/grammar_04_03.mp3"]
        },
        {
            "rule": "Movement Verbs",
            "explanation": "Common verbs used for giving directions",
            "examples": [
                "போங்கள் (pongal) - go",
                "வாங்கள் (vaangal) - come",
                "திரும்புங்கள் (thirumbungal) - turn"
            ],
            "tips": "These are polite command forms ending in ங்கள்",
            "examples_audio_urls": [f"{base_url}/grammar_05_01.mp3", f"{base_url}/grammar_05_02.mp3", f"{base_url}/grammar_05_03.mp3"]
        },
        {
            "rule": "Distance Expressions",
            "explanation": "How to express distance and measurement",
            "examples": [
                "கிலோமீட்டர் (kilomeettar) - kilometer",
                "மீட்டர் (meettar) - meter",
                "தூரம் (thoorem) - distance"
            ],
            "tips": "Use எவ்வளவு (how much) with distance words",
            "examples_audio_urls": [f"{base_url}/grammar_06_01.mp3", f"{base_url}/grammar_06_02.mp3", f"{base_url}/grammar_06_03.mp3"]
        },
        {
            "rule": "Building and Place Types",
            "explanation": "Different types of places and buildings",
            "examples": [
                "கட்டிடம் (kattidam) - building",
                "கடை (kadai) - shop",
                "அலுவலகம் (aluvalagatham) - office"
            ],
            "tips": "Learn common place names for better navigation",
            "examples_audio_urls": [f"{base_url}/grammar_07_01.mp3", f"{base_url}/grammar_07_02.mp3", f"{base_url}/grammar_07_03.mp3"]
        },
        {
            "rule": "Street and Road Terms",
            "explanation": "Different words for roads and pathways",
            "examples": [
                "சாலை (saalai) - main road",
                "தெரு (theru) - street",
                "வழி (vazhi) - way/path"
            ],
            "tips": "சாலை for big roads, தெரு for smaller streets",
            "examples_audio_urls": [f"{base_url}/grammar_08_01.mp3", f"{base_url}/grammar_08_02.mp3", f"{base_url}/grammar_08_03.mp3"]
        },
        {
            "rule": "Floor and Level Indicators",
            "explanation": "How to indicate different floors or levels",
            "examples": [
                "மாடி (maadi) - floor",
                "மேல் (mel) - upper/above",
                "கீழ் (keezh) - lower/below"
            ],
            "tips": "Use ordinal numbers with மாடி: முதல் மாடி (first floor)",
            "examples_audio_urls": [f"{base_url}/grammar_09_01.mp3", f"{base_url}/grammar_09_02.mp3", f"{base_url}/grammar_09_03.mp3"]
        },
        {
            "rule": "Polite Direction Requests",
            "explanation": "Polite ways to ask for and give directions",
            "examples": [
                "மன்னிக்கவும் (mannikkavum) - excuse me",
                "தயவுசெய்து (thayavuseydu) - please",
                "நன்றி (nanRi) - thank you"
            ],
            "tips": "Always start with மன்னிக்கவும் when asking strangers",
            "examples_audio_urls": [f"{base_url}/grammar_10_01.mp3", f"{base_url}/grammar_10_02.mp3", f"{base_url}/grammar_10_03.mp3"]
        }
    ]

    # ✅ EXERCISES - ALL 24 UNIQUE with romanized Tamil in explanations
    exercises = [
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "What is the Tamil word for 'north'?",
            "options": ["வடக்கு", "தெற்கு", "கிழக்கு", "மேற்கு"],
            "correctAnswer": 0,
            "explanation": "வடக்கு (vadakku) means north in Tamil",
            "options_audio_urls": [f"{base_url}/exercise_01_option_01.mp3", f"{base_url}/exercise_01_option_02.mp3", f"{base_url}/exercise_01_option_03.mp3", f"{base_url}/exercise_01_option_04.mp3"]
        },
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "How do you say 'turn left' in Tamil?",
            "options": ["வலது பக்கம் திரும்புங்கள்", "இடது பக்கம் திரும்புங்கள்", "நேராக போங்கள்", "பின்னால் போங்கள்"],
            "correctAnswer": 1,
            "explanation": "இடது பக்கம் திரும்புங்கள் (idathu pakkam thirumbungal) means turn left",
            "options_audio_urls": [f"{base_url}/exercise_02_option_01.mp3", f"{base_url}/exercise_02_option_02.mp3", f"{base_url}/exercise_02_option_03.mp3", f"{base_url}/exercise_02_option_04.mp3"]
        },
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "What does 'சாலை' mean?",
            "options": ["சாலை", "தெரு", "பாலம்", "கட்டிடம்"],
            "correctAnswer": 0,
            "explanation": "சாலை (saalai) means road in Tamil",
            "options_audio_urls": [f"{base_url}/exercise_03_option_01.mp3", f"{base_url}/exercise_03_option_02.mp3", f"{base_url}/exercise_03_option_03.mp3", f"{base_url}/exercise_03_option_04.mp3"]
        },
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "Which word means 'east' in Tamil?",
            "options": ["வடக்கு", "தெற்கு", "கிழக்கு", "மேற்கு"],
            "correctAnswer": 2,
            "explanation": "கிழக்கு (kizhakku) means east in Tamil",
            "options_audio_urls": [f"{base_url}/exercise_04_option_01.mp3", f"{base_url}/exercise_04_option_02.mp3", f"{base_url}/exercise_04_option_03.mp3", f"{base_url}/exercise_04_option_04.mp3"]
        },
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "How do you ask 'Where is it?' in Tamil?",
            "options": ["எங்கே இருக்கிறது?", "என்ன இருக்கிறது?", "எப்போது இருக்கிறது?", "எப்படி இருக்கிறது?"],
            "correctAnswer": 0,
            "explanation": "எங்கே இருக்கிறது? (engE irukkiRadhu?) means where is it?",
            "options_audio_urls": [f"{base_url}/exercise_05_option_01.mp3", f"{base_url}/exercise_05_option_02.mp3", f"{base_url}/exercise_05_option_03.mp3", f"{base_url}/exercise_05_option_04.mp3"]
        },
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "What does 'அருகில்' mean?",
            "options": ["அருகில்", "தூரம்", "எதிரில்", "பின்"],
            "correctAnswer": 0,
            "explanation": "அருகில் (arugil) means near in Tamil",
            "options_audio_urls": [f"{base_url}/exercise_06_option_01.mp3", f"{base_url}/exercise_06_option_02.mp3", f"{base_url}/exercise_06_option_03.mp3", f"{base_url}/exercise_06_option_04.mp3"]
        },
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "Which word means 'straight' in Tamil?",
            "options": ["இடது", "வலது", "நேராக", "திரும்பு"],
            "correctAnswer": 2,
            "explanation": "நேராக (neraaga) means straight in Tamil",
            "options_audio_urls": [f"{base_url}/exercise_07_option_01.mp3", f"{base_url}/exercise_07_option_02.mp3", f"{base_url}/exercise_07_option_03.mp3", f"{base_url}/exercise_07_option_04.mp3"]
        },
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "What does 'பாலம்' mean?",
            "options": ["பாலம்", "சாலை", "தெரு", "கட்டிடம்"],
            "correctAnswer": 0,
            "explanation": "பாலம் (paalam) means bridge in Tamil",
            "options_audio_urls": [f"{base_url}/exercise_08_option_01.mp3", f"{base_url}/exercise_08_option_02.mp3", f"{base_url}/exercise_08_option_03.mp3", f"{base_url}/exercise_08_option_04.mp3"]
        },
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "How do you say 'behind' in Tamil?",
            "options": ["முன்", "பின்", "மேல்", "கீழ்"],
            "correctAnswer": 1,
            "explanation": "பின் (pin) means behind in Tamil",
            "options_audio_urls": [f"{base_url}/exercise_09_option_01.mp3", f"{base_url}/exercise_09_option_02.mp3", f"{base_url}/exercise_09_option_03.mp3", f"{base_url}/exercise_09_option_04.mp3"]
        },
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "What does 'முக்கோணம்' mean?",
            "options": ["முக்கோணம்", "மூலை", "நடு", "எதிர்"],
            "correctAnswer": 0,
            "explanation": "முக்கோணம் (mukkonam) means junction in Tamil",
            "options_audio_urls": [f"{base_url}/exercise_10_option_01.mp3", f"{base_url}/exercise_10_option_02.mp3", f"{base_url}/exercise_10_option_03.mp3", f"{base_url}/exercise_10_option_04.mp3"]
        },
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "Which word means 'distance' in Tamil?",
            "options": ["இடம்", "வழி", "தூரம்", "திசை"],
            "correctAnswer": 2,
            "explanation": "தூரம் (thoorem) means distance in Tamil",
            "options_audio_urls": [f"{base_url}/exercise_11_option_01.mp3", f"{base_url}/exercise_11_option_02.mp3", f"{base_url}/exercise_11_option_03.mp3", f"{base_url}/exercise_11_option_04.mp3"]
        },
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "How do you say 'opposite' in Tamil?",
            "options": ["அருகில்", "எதிர்", "பின்", "முன்"],
            "correctAnswer": 1,
            "explanation": "எதிர் (edhir) means opposite in Tamil",
            "options_audio_urls": [f"{base_url}/exercise_12_option_01.mp3", f"{base_url}/exercise_12_option_02.mp3", f"{base_url}/exercise_12_option_03.mp3", f"{base_url}/exercise_12_option_04.mp3"]
        },
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "What does 'கட்டிடம்' mean?",
            "options": ["கட்டிடம்", "கடை", "அலுவலகம்", "வீடு"],
            "correctAnswer": 0,
            "explanation": "கட்டிடம் (kattidam) means building in Tamil",
            "options_audio_urls": [f"{base_url}/exercise_13_option_01.mp3", f"{base_url}/exercise_13_option_02.mp3", f"{base_url}/exercise_13_option_03.mp3", f"{base_url}/exercise_13_option_04.mp3"]
        },
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "Which word means 'corner' in Tamil?",
            "options": ["மூலை", "நடு", "பக்கம்", "இடம்"],
            "correctAnswer": 0,
            "explanation": "மூலை (moolai) means corner in Tamil",
            "options_audio_urls": [f"{base_url}/exercise_14_option_01.mp3", f"{base_url}/exercise_14_option_02.mp3", f"{base_url}/exercise_14_option_03.mp3", f"{base_url}/exercise_14_option_04.mp3"]
        },
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "How do you say 'address' in Tamil?",
            "options": ["முகவரி", "தூரம்", "இடம்", "வழி"],
            "correctAnswer": 0,
            "explanation": "முகவரி (mugavari) means address in Tamil",
            "options_audio_urls": [f"{base_url}/exercise_15_option_01.mp3", f"{base_url}/exercise_15_option_02.mp3", f"{base_url}/exercise_15_option_03.mp3", f"{base_url}/exercise_15_option_04.mp3"]
        },
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "What does 'தெரு' mean?",
            "options": ["தெரு", "சாலை", "பாலம்", "வழி"],
            "correctAnswer": 0,
            "explanation": "தெரு (theru) means street in Tamil",
            "options_audio_urls": [f"{base_url}/exercise_16_option_01.mp3", f"{base_url}/exercise_16_option_02.mp3", f"{base_url}/exercise_16_option_03.mp3", f"{base_url}/exercise_16_option_04.mp3"]
        },
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "Which word means 'above' in Tamil?",
            "options": ["கீழ்", "மேல்", "நடு", "பக்கம்"],
            "correctAnswer": 1,
            "explanation": "மேல் (mel) means above in Tamil",
            "options_audio_urls": [f"{base_url}/exercise_17_option_01.mp3", f"{base_url}/exercise_17_option_02.mp3", f"{base_url}/exercise_17_option_03.mp3", f"{base_url}/exercise_17_option_04.mp3"]
        },
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "How do you say 'excuse me' in Tamil?",
            "options": ["நன்றி", "மன்னிக்கவும்", "தயவுசெய்து", "வணக்கம்"],
            "correctAnswer": 1,
            "explanation": "மன்னிக்கவும் (mannikkavum) means excuse me in Tamil",
            "options_audio_urls": [f"{base_url}/exercise_18_option_01.mp3", f"{base_url}/exercise_18_option_02.mp3", f"{base_url}/exercise_18_option_03.mp3", f"{base_url}/exercise_18_option_04.mp3"]
        },
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "What does 'வழி' mean?",
            "options": ["வழி", "இடம்", "திசை", "தூரம்"],
            "correctAnswer": 0,
            "explanation": "வழி (vazhi) means way or path in Tamil",
            "options_audio_urls": [f"{base_url}/exercise_19_option_01.mp3", f"{base_url}/exercise_19_option_02.mp3", f"{base_url}/exercise_19_option_03.mp3", f"{base_url}/exercise_19_option_04.mp3"]
        },
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "Which word means 'middle' in Tamil?",
            "options": ["முன்", "பின்", "நடு", "பக்கம்"],
            "correctAnswer": 2,
            "explanation": "நடு (nadu) means middle in Tamil",
            "options_audio_urls": [f"{base_url}/exercise_20_option_01.mp3", f"{base_url}/exercise_20_option_02.mp3", f"{base_url}/exercise_20_option_03.mp3", f"{base_url}/exercise_20_option_04.mp3"]
        },
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "How do you say 'below' in Tamil?",
            "options": ["மேல்", "கீழ்", "நடு", "பக்கம்"],
            "correctAnswer": 1,
            "explanation": "கீழ் (keezh) means below in Tamil",
            "options_audio_urls": [f"{base_url}/exercise_21_option_01.mp3", f"{base_url}/exercise_21_option_02.mp3", f"{base_url}/exercise_21_option_03.mp3", f"{base_url}/exercise_21_option_04.mp3"]
        },
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "What does 'இடம்' mean?",
            "options": ["இடம்", "வழி", "திசை", "தூரம்"],
            "correctAnswer": 0,
            "explanation": "இடம் (idam) means place in Tamil",
            "options_audio_urls": [f"{base_url}/exercise_22_option_01.mp3", f"{base_url}/exercise_22_option_02.mp3", f"{base_url}/exercise_22_option_03.mp3", f"{base_url}/exercise_22_option_04.mp3"]
        },
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "Which word means 'front' in Tamil?",
            "options": ["முன்", "பின்", "மேல்", "கீழ்"],
            "correctAnswer": 0,
            "explanation": "முன் (mun) means front in Tamil",
            "options_audio_urls": [f"{base_url}/exercise_23_option_01.mp3", f"{base_url}/exercise_23_option_02.mp3", f"{base_url}/exercise_23_option_03.mp3", f"{base_url}/exercise_23_option_04.mp3"]
        },
        {
            "type": "multiple_choice",
            "points": 10,
            "question": "How do you say 'direction' in Tamil?",
            "options": ["திசை", "வழி", "இடம்", "தூரம்"],
            "correctAnswer": 0,
            "explanation": "திசை (thisai) means direction in Tamil",
            "options_audio_urls": [f"{base_url}/exercise_24_option_01.mp3", f"{base_url}/exercise_24_option_02.mp3", f"{base_url}/exercise_24_option_03.mp3", f"{base_url}/exercise_24_option_04.mp3"]
        }
    ]

    return {
        "vocabulary": vocabulary,
        "conversations": conversations,
        "grammar_points": grammar_points,
        "exercises": exercises,
        "lesson_id": lesson_id,
        "lesson_slug": lesson_slug
    }

def update_lesson_in_database(lesson_data):
    """Update the lesson in Supabase database"""

    content_metadata = {
        "vocabulary": lesson_data["vocabulary"],
        "conversations": lesson_data["conversations"],
        "grammar_points": lesson_data["grammar_points"],
        "exercises": lesson_data["exercises"]
    }

    # Supabase API details
    url = "https://lyaojebttnqilmdosmjk.supabase.co/rest/v1/lessons"
    headers = {
        "apikey": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c",
        "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c",
        "Content-Type": "application/json",
        "Prefer": "return=minimal"
    }

    # Update lesson
    update_data = {"content_metadata": content_metadata}
    response = requests.patch(
        f"{url}?id=eq.{lesson_data['lesson_id']}",
        headers=headers,
        json=update_data
    )

    return response.status_code == 204

if __name__ == "__main__":
    print("🎯 Creating PERFECT FIXED Directions and Locations Lesson...")
    lesson_data = create_fixed_directions_lesson()

    print("✅ PERFECT lesson created with:")
    print(f"📚 {len(lesson_data['vocabulary'])} vocabulary items")
    print(f"💬 {len(lesson_data['conversations'])} UNIQUE conversations with romanized Tamil")
    print(f"📖 {len(lesson_data['grammar_points'])} UNIQUE grammar points")
    print(f"🎯 {len(lesson_data['exercises'])} UNIQUE exercises with romanized Tamil")

    print("\n🔄 Updating lesson in database...")
    success = update_lesson_in_database(lesson_data)

    if success:
        print("✅ SUCCESS: PERFECT Directions lesson updated!")
        print("🎉 NO REPEATS - ALL CONTENT IS UNIQUE!")
        print("🎉 ALL conversations have romanized Tamil!")
        print("🎉 ALL exercises have romanized Tamil in explanations!")
        print("🎯 Ready for your quality check!")
    else:
        print("❌ FAILED: Could not update lesson in database")
