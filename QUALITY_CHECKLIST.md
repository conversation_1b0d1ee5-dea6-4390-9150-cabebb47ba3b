# 📋 NIRA LESSON QUALITY CHECKLIST
## Based on Working Animals & Nature Lesson

### ✅ VOCABULARY REQUIREMENTS (25 items)
- [ ] **Exactly 25 vocabulary items**
- [ ] Each item has: `word`, `translation`, `pronunciation`, `example`, `difficulty`, `part_of_speech`
- [ ] **Tamil text is authentic** (not romanized)
- [ ] **Pronunciation is in romanized Tamil** (e.g., "naai", "poonai")
- [ ] **Examples are complete Tamil sentences** with English translation in parentheses
- [ ] **Audio URLs follow exact pattern**: `word_audio_url` and `example_audio_url`
- [ ] **All examples are topic-specific** (not generic)
- [ ] **No repetition** - each vocabulary item is unique

### ✅ CONVERSATIONS REQUIREMENTS (15 conversations)
- [ ] **Exactly 15 conversation items**
- [ ] Each has: `title`, `scenario`, `exchanges`, `difficulty`
- [ ] **First 2 conversations are unique and topic-specific**
- [ ] **Remaining 13 can be generated with loop** but with topic-specific content
- [ ] Each exchange has: `text`, `speaker`, `translation`, `pronunciation`, `audio_url`
- [ ] **Tamil text is authentic** (not romanized)
- [ ] **Pronunciation is romanized Tamil**
- [ ] **Audio URLs follow pattern**: `conv_{i:02d}_{j:02d}.mp3`
- [ ] **All conversations relate to lesson topic**

### ✅ GRAMMAR REQUIREMENTS (10 points)
- [ ] **Exactly 10 grammar points**
- [ ] Each has: `rule`, `explanation`, `examples`, `tips`, `examples_audio_urls`
- [ ] **First 2 grammar points are unique and topic-specific**
- [ ] **Remaining 8 can be generated with loop** but with topic-specific content
- [ ] **Examples are in Tamil with English translations**
- [ ] **Audio URLs follow pattern**: `grammar_{i:02d}_{j:02d}.mp3`
- [ ] **All grammar relates to lesson topic**

### ✅ EXERCISES REQUIREMENTS (24 exercises)
- [ ] **Exactly 24 exercise items**
- [ ] Each has: `type`, `points`, `question`, `options`, `correctAnswer`, `explanation`, `options_audio_urls`
- [ ] **First 2 exercises are unique and topic-specific**
- [ ] **Remaining 22 can be generated with loop** but with topic-specific content
- [ ] **Questions are in English**
- [ ] **Options are in Tamil**
- [ ] **Explanations include Tamil word with pronunciation**
- [ ] **Audio URLs follow pattern**: `exercise_{i:02d}_option_{j:02d}.mp3`
- [ ] **All exercises test lesson vocabulary**

### ✅ CONTENT QUALITY STANDARDS
- [ ] **NO English examples in Tamil sections**
- [ ] **NO placeholder content** (like "example", "sample")
- [ ] **NO repetitive content** across items
- [ ] **ALL Tamil text is authentic** and grammatically correct
- [ ] **ALL pronunciations are accurate romanized Tamil**
- [ ] **ALL content is topic-specific** to the lesson
- [ ] **ALL audio URLs follow exact naming convention**

### ✅ TECHNICAL REQUIREMENTS
- [ ] **Base URL**: `https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/{lesson_slug}/`
- [ ] **Vocabulary audio**: `vocab_{i:02d}_word.mp3` and `vocab_{i:02d}_example.mp3`
- [ ] **Conversation audio**: `conv_{i:02d}_{j:02d}.mp3`
- [ ] **Grammar audio**: `grammar_{i:02d}_{j:02d}.mp3`
- [ ] **Exercise audio**: `exercise_{i:02d}_option_{j:02d}.mp3`
- [ ] **All arrays have correct lengths**: 25, 15, 10, 24

### ❌ COMMON MISTAKES TO AVOID
- [ ] **NO romanized Tamil in word fields** (should be Tamil script)
- [ ] **NO English examples in Tamil sections**
- [ ] **NO generic examples** (must be topic-specific)
- [ ] **NO repetitive conversations** (first 2 must be unique)
- [ ] **NO placeholder content**
- [ ] **NO incorrect audio URL patterns**
- [ ] **NO wrong array lengths**

## 🎯 SUCCESS CRITERIA
A lesson passes ONLY if:
1. ✅ All 25 vocabulary items are authentic Tamil with topic-specific examples
2. ✅ All 15 conversations have unique, topic-specific content
3. ✅ All 10 grammar points relate to the lesson topic
4. ✅ All 24 exercises test lesson vocabulary correctly
5. ✅ ALL content is authentic Tamil (no English in Tamil fields)
6. ✅ ALL audio URLs follow exact naming convention
7. ✅ NO repetitive or placeholder content anywhere
